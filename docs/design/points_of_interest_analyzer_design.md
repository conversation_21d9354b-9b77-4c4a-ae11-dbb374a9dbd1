# Points of Interest Analyzer Design

## Overview

The Points of Interest (POI) Analyzer is a high-level orchestration layer that collates data from existing analyzers to identify and prioritize events, patterns, and anomalies relevant to investigation workflows. Rather than implementing new analysis algorithms, it focuses on intelligent data aggregation and relevance scoring to support the investigation-focused workflow outlined in the investigation workflow proposal.

## Design Principles

### 1. Data Orchestration, Not Duplication
The POI analyzer serves as an intelligent coordinator that leverages existing analyzer capabilities rather than reimplementing analysis logic. It focuses on:
- **Temporal coordination**: Aligning data from multiple sources around investigation time windows
- **Relevance scoring**: Prioritizing events based on investigation context
- **Cross-source correlation**: Identifying relationships between different data types

### 2. Investigation-Centric Design
Traditional log analysis follows a "browse all data" approach. The POI analyzer transforms this into an "investigate this moment" approach by:
- **Temporal centering**: Focusing analysis around specific points in time
- **Contextual windowing**: Providing relevant data within configurable time windows
- **Progressive disclosure**: Supporting drill-down from high-level summaries to detailed analysis

### 3. Extensible Architecture
The design supports incremental enhancement by:
- **Modular scoring algorithms**: New relevance scoring methods can be added without affecting core functionality
- **Configurable data sources**: New analyzer types can be integrated through standardized interfaces
- **Plugin-based extensions**: Additional analysis capabilities can be added without modifying core orchestration logic

## Core Components

### Investigation Context Builder

**Purpose**: Aggregate all relevant information around a specific point in time for investigation.

**Input Requirements**:
- Target timestamp for investigation focus
- Time window configuration (default ±60 minutes)
- Optional position filter for spatial context
- Optional event type filters

**Data Sources and Orchestration**:
- **Critical Events**: Uses `structured_log_analyzer.get_log_level_counts()` and `structured_log_analyzer.detect_log_level_anomalies()` for ERROR/WARNING events within time window
- **Active Timeline Events**: Uses `timeline_analyzer.query_timeline_events()` for events spanning target time and `timeline_analyzer.query_markers()` for markers within window, creating hierarchical relationships
- **Resource State**: Uses `resource_analyzer.get_system_overview()` and `resource_analyzer.get_resource_column()` for system metrics at target time
- **Concurrent Activity**: Uses `structured_log_analyzer.query()` across positions and `cross_analyzer.get_log_summary()` for cross-position event correlation
- **Recent Anomalies**: Uses `structured_log_analyzer.detect_time_series_anomalies()` and `resource_analyzer.detect_time_series_anomalies()` within expanded time window

**Output Structure**:
- Temporal context (target time, window boundaries)
- Prioritized event lists with relevance scores
- System state snapshot
- Cross-position activity summary
- Anomaly summary with severity indicators

### Event Burst Detection

**Purpose**: Identify rapid increases in event counts that may indicate system issues.

**Algorithm Requirements**:
- **Sliding Window Analysis**: Use configurable time windows (default 5 minutes) to detect event count increases
- **Threshold-Based Detection**: Configurable count thresholds (default 3 events) to trigger burst identification
- **Event Type Filtering**: Support filtering by specific log event types or levels
- **Temporal Clustering**: Group adjacent burst windows to identify sustained periods of high activity

**Raw Data Availability**: 
- ✅ **Available**: Structured log analyzer provides timestamped events with type information
- ✅ **Available**: Timeline analyzer provides timestamped markers
- ❌ **Missing**: No existing burst detection algorithm, requires implementation

**Implementation Approach**:
- Uses `structured_log_analyzer.query()` to get events within time range
- Apply sliding window analysis with configurable parameters
- Identify windows where event counts exceed thresholds
- Cluster adjacent burst windows to identify sustained issues
- Calculate burst intensity metrics (peak rate, duration, total count)

### Console Application Integration

**Purpose**: Provide comprehensive investigation reporting through console application collation of multiple API endpoints.

**Console Capabilities**:
- **Multi-API Orchestration**: Console app calls multiple POI endpoints and combines results
- **Rich Formatting**: Hierarchical display of timeline events with associated markers
- **Flexible Reporting**: Support for investigation-context only, burst-detection only, or comprehensive modes
- **Error Handling**: Graceful degradation when individual APIs fail, showing partial results

**Data Collation Strategy**:
- Investigation context from `/poi/investigation-context` endpoint
- Event burst data from `/poi/event-bursts` endpoint
- Combined formatting with relevance-based prioritization
- Cross-referenced timeline events and markers for hierarchical display

### Point-in-Time State Analysis

**Purpose**: Provide system state snapshots at specific timestamps.

**Algorithm Requirements**:
- **Temporal Interpolation**: Estimate metric values at specific timestamps when exact matches aren't available
- **State Reconstruction**: Combine resource metrics, active timeline events, and recent log events
- **Baseline Comparison**: Compare current state against historical normal ranges
- **Completeness Assessment**: Identify missing data sources or incomplete information

**Raw Data Availability**:
- ✅ **Available**: Resource metrics with timestamps
- ✅ **Available**: Timeline events with start/end times
- ✅ **Available**: Log events with timestamps
- ❌ **Missing**: Interpolation algorithms for sparse data
- ❌ **Missing**: Baseline calculation methods

### Concurrent Activity Analysis

**Purpose**: Identify what else was happening across all positions at the same time.

**Algorithm Requirements**:
- **Temporal Tolerance**: Group events within configurable time tolerance windows
- **Cross-Position Correlation**: Identify simultaneous events across different positions
- **Activity Clustering**: Group related activities that occurred concurrently
- **Outlier Detection**: Identify positions with unusual activity patterns

**Raw Data Availability**:
- ✅ **Available**: Position-tagged events from structured log analyzer
- ✅ **Available**: Position-tagged timeline events
- ✅ **Available**: Cross-analyzer provides position enumeration
- ❌ **Missing**: Cross-position correlation algorithms
- ❌ **Missing**: Position-level activity comparison methods

### Event Relevance Scoring

**Purpose**: Prioritize events and information based on investigation context.

**Scoring Algorithm Components**:
- **Temporal Proximity**: Events closer to target time receive higher scores
- **Severity Weighting**: ERROR > WARNING > INFO for log events
- **Anomaly Magnitude**: Statistical significance of detected anomalies
- **Timeline Context**: Events within active timeline processes receive higher scores
- **Cross-Position Correlation**: Events affecting multiple positions receive higher scores

**Scoring Methodology**:
- **Composite Scoring**: Combine multiple scoring factors with configurable weights
- **Normalization**: Ensure scores are comparable across different event types
- **Thresholding**: Apply minimum relevance thresholds to filter noise
- **Contextual Adjustment**: Adjust scoring based on investigation focus and temporal context

**Raw Data Availability**:
- ✅ **Available**: Event timestamps for temporal proximity calculation
- ✅ **Available**: Log levels for severity weighting
- ✅ **Available**: Anomaly detection results with statistical measures
- ✅ **Available**: Timeline event containment information
- ❌ **Missing**: Cross-position correlation metrics
- ❌ **Missing**: Composite scoring algorithms

## Data Integration Requirements

### Existing Analyzer Capabilities

**Structured Log Analyzer**:
- ✅ Time-based event querying
- ✅ Log level filtering and counting
- ✅ Position-based filtering
- ✅ Anomaly detection for log patterns
- ✅ Event timeline generation

**Resource Analyzer**:
- ✅ Time-based metric querying
- ✅ System overview generation
- ✅ Anomaly detection for resource metrics
- ✅ Column availability analysis

**Timeline Analyzer**:
- ✅ Timeline event querying with time filters
- ✅ Marker querying with time filters
- ✅ Event hierarchy and containment
- ✅ Duration statistics
- ✅ Metadata-based filtering

**Cross Analyzer**:
- ✅ Common time range calculation
- ✅ Cross-source data availability
- ✅ Multi-source summary generation
- ✅ Cached result management

### Missing Capabilities

**Temporal Analysis**:
- ❌ Point-in-time state reconstruction
- ❌ Temporal interpolation for sparse data
- ❌ Baseline calculation and comparison
- ❌ Time window optimization algorithms

**Cross-Position Analysis**:
- ❌ Position outlier detection
- ❌ Cross-position event correlation
- ❌ Concurrent activity clustering
- ❌ Position behavior comparison

**Investigation-Specific Algorithms**:
- ❌ Event burst detection
- ❌ Relevance scoring algorithms
- ❌ Context-aware data prioritization
- ❌ Multi-source data orchestration logic

## Implementation Strategy

### Phase 1: Core Orchestration
- Implement Investigation Context Builder using existing analyzer capabilities
- Create basic event burst detection algorithm
- Develop simple relevance scoring based on temporal proximity and severity
- Create API endpoints and console integration for comprehensive reporting

### Phase 2: Enhanced Analysis
- Add point-in-time state reconstruction
- Implement cross-position correlation algorithms
- Develop baseline comparison methods
- Enhance relevance scoring with anomaly magnitude and timeline context

### Phase 3: Advanced Features
- Add position outlier detection
- Implement concurrent activity clustering
- Develop contextual scoring adjustments
- Add machine learning-based pattern recognition

### Phase 4: Optimization
- Implement result caching for investigation contexts
- Add temporal interpolation for sparse data
- Optimize cross-position analysis performance
- Develop adaptive time window algorithms

## Algorithm Specifications

### Burst Detection Algorithm
**Input**: Event stream with timestamps, window size, threshold count
**Process**: 
1. Apply sliding window analysis over event timestamps
2. Count events within each window
3. Identify windows exceeding threshold
4. Cluster adjacent high-activity windows
5. Calculate burst metrics (intensity, duration, peak rate)

**Output**: List of burst periods with metrics and affected event types

### Relevance Scoring Algorithm
**Input**: Event list, investigation context (target time, window, position)
**Process**:
1. Calculate temporal proximity scores (inverse distance from target time)
2. Apply severity weights (ERROR=1.0, WARNING=0.7, INFO=0.3)
3. Include anomaly magnitude if available
4. Apply timeline context multipliers
5. Normalize scores to 0-1 range

**Output**: Events with relevance scores for prioritization

### Baseline Comparison Algorithm
**Input**: Current metric values, historical data, comparison period
**Process**:
1. Calculate historical statistics (mean, std dev, percentiles)
2. Compare current values against historical ranges
3. Identify values outside normal ranges
4. Calculate deviation magnitude
5. Apply statistical significance testing

**Output**: Comparison results with deviation metrics and significance indicators

## Performance Considerations

### Caching Strategy
- Cache investigation contexts for frequently accessed time ranges
- Pre-calculate baselines for common time periods
- Store relevance scores for repeated queries
- Implement cache invalidation based on data freshness

### Query Optimization
- Use existing analyzer query methods efficiently
- Minimize data retrieval through targeted time windows
- Leverage existing cross-analyzer caching
- Implement lazy loading for detailed analysis

### Scalability Constraints
- Algorithm complexity should scale linearly with time window size
- Cross-position analysis limited by position count
- Burst detection bounded by event frequency
- Relevance scoring complexity depends on event count within window

## Success Metrics

### Functional Metrics
- **Investigation Context Completeness**: Percentage of relevant events captured
- **Relevance Scoring Accuracy**: User validation of event prioritization
- **API Response Completeness**: Success rate of data orchestration across multiple analyzers
- **Cross-Position Correlation Accuracy**: Validation of concurrent activity detection

### Performance Metrics
- **Query Response Time**: Time to generate investigation context
- **Cache Hit Rate**: Effectiveness of caching strategy
- **Memory Usage**: Resource consumption during analysis
- **Scalability Limits**: Performance degradation with data volume

### User Experience Metrics
- **Time to Investigation**: Reduction in investigation setup time
- **Investigation Success Rate**: Percentage of investigations leading to root cause identification
- **User Satisfaction**: Feedback on investigation workflow efficiency
- **Feature Adoption**: Usage rates of POI analyzer capabilities

## Future Enhancements

### Machine Learning Integration
- Pattern recognition for recurring investigation scenarios
- Predictive modeling for investigation relevance
- Automated baseline adjustment based on system behavior
- Anomaly detection improvement through historical learning

### External Integration
- API endpoints for external investigation tools
- Integration with incident management systems
- Export capabilities for investigation reports
- Webhook support for real-time investigation triggers

### Advanced Analytics
- Causal analysis for event propagation
- Statistical modeling for pattern detection
- Time series forecasting for predictive investigation
- Network analysis for cross-position relationships

## Conclusion

The Points of Interest Analyzer represents a strategic evolution from general-purpose log analysis to investigation-focused workflow support. By leveraging existing analyzer capabilities and adding intelligent orchestration, it provides the foundation for efficient incident investigation while maintaining the flexibility to evolve with changing requirements.

The design emphasizes practical implementation using available data sources while identifying specific gaps that need to be addressed. The modular architecture supports incremental development and testing, allowing for gradual enhancement of investigation capabilities based on user feedback and operational experience.