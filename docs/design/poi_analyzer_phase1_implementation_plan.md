# Points of Interest Analyzer - Phase 1 Implementation Plan

## Overview

Phase 1 focuses on implementing the core orchestration capabilities of the POI analyzer using existing analyzer methods. This phase establishes the foundation for investigation-focused workflows while minimizing risk by leveraging proven analysis capabilities.

## Phase 1 Scope

### Core Deliverables
1. **PointsOfInterestAnalyzer class** - Main orchestration layer
2. **Investigation Context Builder** - Primary investigation API
3. **Basic Event Burst Detection** - Rapid event increase detection
4. **Simple Relevance Scoring** - Temporal proximity and severity-based scoring
5. **Investigation Presets** - Common investigation scenarios
6. **Integration Tests** - Validation of orchestration capabilities

### Excluded from Phase 1
- Advanced machine learning algorithms
- Complex baseline comparison methods
- Cross-position correlation algorithms
- Performance optimization and caching
- UI integration (focus on API layer)

## Implementation Tasks

### Task 1: Core Infrastructure Setup
**Duration**: 1-2 days
**Dependencies**: None

**Subtasks**:
1. **Create base POI analyzer class**
   - Define constructor accepting existing analyzers
   - Implement analyzer validation and initialization
   - Add basic logging and error handling
   - Set up analyzer_type property for consistency

2. **Add analyzer factory integration**
   - Update analyzer_factory.py to support POI analyzer creation
   - Ensure proper dependency injection for existing analyzers
   - Add POI analyzer to available analyzer types

3. **Create basic test structure**
   - Set up test fixtures with sample data
   - Create mock analyzer instances for testing
   - Implement basic instantiation tests

**Success Criteria**:
- POI analyzer can be instantiated with existing analyzers
- Factory pattern works correctly
- Basic tests pass

### Task 2: Investigation Context Builder
**Duration**: 3-4 days
**Dependencies**: Task 1 complete

**Subtasks**:
1. **Implement get_investigation_context method**
   - Accept target_time, window_minutes, position parameters
   - Calculate time window boundaries
   - Orchestrate calls to existing analyzers
   - Return structured context dictionary

2. **Critical events collection**
   - Query structured_log_analyzer.get_log_level_counts() for ERROR/WARNING
   - Query structured_log_analyzer.detect_log_level_anomalies() for anomalous periods
   - Filter events within investigation window
   - Sort by timestamp and severity

3. **Active timeline events collection**
   - Query timeline_analyzer.query_timeline_events() for events that span, start, or end during target time (broader context)
   - Filter timeline events by position if position parameter specified
   - Include incomplete timeline events (end_time is None) that started before investigation window
   - For each active timeline event, query timeline_analyzer.query_markers() for markers within investigation window
   - Only include markers that belong to the active timeline events (not orphaned markers)
   - Associate markers with their parent timeline events for hierarchical context
   - Distinguish between "active processes" (timeline events) and "events during investigation" (markers)

4. **Resource snapshot collection**
   - Query resource_analyzer.get_system_overview() for target time window
   - Query resource_analyzer.get_resource_column() for key metrics at target time
   - Collect CPU, memory, disk usage at investigation time
   - Format for investigation context

5. **Concurrent activity collection**
   - Query structured_log_analyzer across all positions within time window
   - Group events by position and timestamp
   - Identify simultaneous activities across positions
   - Filter for significant events only

**Success Criteria**:
- Investigation context returns comprehensive data structure
- All existing analyzer methods are called correctly
- Data is properly filtered by time window
- Context includes all required sections

### Task 3: Event Burst Detection
**Duration**: 2-3 days
**Dependencies**: Task 2 complete

**Subtasks**:
1. **Implement detect_event_bursts method**
   - Accept window_minutes, threshold_count, event_types parameters
   - Support optional time range filtering
   - Return list of burst periods with metadata

2. **Sliding window analysis algorithm**
   - Query structured_log_analyzer for events in time range
   - Apply sliding window with configurable step size
   - Count events within each window
   - Identify windows exceeding threshold

3. **Burst clustering algorithm**
   - Group adjacent high-activity windows
   - Calculate burst duration and intensity
   - Identify peak activity periods within bursts
   - Filter out isolated spikes

4. **Burst metadata enrichment**
   - Include event types involved in each burst
   - Calculate burst intensity metrics (events per minute)
   - Identify affected positions
   - Add temporal context (start/end times)

**Success Criteria**:
- Burst detection identifies rapid event increases
- Algorithm handles various window sizes and thresholds
- Burst metadata includes relevant context
- Method integrates with investigation context

### Task 4: Simple Relevance Scoring
**Duration**: 2-3 days
**Dependencies**: Task 2 complete

**Subtasks**:
1. **Implement calculate_relevance_scores method**
   - Accept events list and investigation context
   - Apply temporal proximity scoring
   - Apply severity weighting
   - Return events with relevance scores

2. **Temporal proximity scoring**
   - Calculate time distance from target time
   - Apply inverse distance weighting
   - Normalize scores to 0-1 range
   - Handle edge cases (same timestamp)

3. **Severity weighting algorithm**
   - Define severity weights (ERROR=1.0, WARNING=0.7, INFO=0.3)
   - Apply weights to log level events
   - Handle timeline events and markers
   - Provide configurable weight overrides

4. **Score normalization and ranking**
   - Combine temporal and severity scores
   - Normalize final scores to consistent range
   - Sort events by relevance score
   - Apply minimum relevance threshold filtering

**Success Criteria**:
- Relevance scores prioritize events appropriately
- Temporal proximity affects scoring correctly
- Severity weighting works as expected
- Scores are normalized and comparable

### Task 5: Console Report Collation
**Duration**: 1-2 days
**Dependencies**: Tasks 2, 3, 4 complete

**Subtasks**:
1. **Implement console report collation logic**
   - Call multiple POI API endpoints from console app
   - Combine investigation context and event burst data
   - Format for comprehensive console display
   - Handle API errors gracefully with partial reports

2. **Report formatting optimization**
   - Create rich console formatting for combined data
   - Organize data by relevance and type
   - Include summary statistics and counts
   - Provide hierarchical display of timeline events and markers

3. **Console command modes**
   - Support investigation context only mode
   - Support event bursts only mode  
   - Support combined comprehensive mode
   - Allow flexible time range and position filtering

4. **Error handling and performance**
   - Handle partial data when some APIs fail
   - Show progress indicators for multiple API calls
   - Implement reasonable timeouts
   - Provide options to limit data collection scope

**Success Criteria**:
- Console can collate data from multiple POI endpoints
- Comprehensive reporting works with combined API calls
- Performance is acceptable for typical scenarios
- Error handling works when individual APIs fail

### Task 6: API Routes and Console Integration
**Duration**: 2-3 days
**Dependencies**: Tasks 2, 3, 4, 5 complete

**Subtasks**:
1. **Create POI API routes**
   - Add new route file `backend/app/api/routes/points_of_interest.py`
   - Implement `/poi/investigation-context` endpoint
   - Implement `/poi/event-bursts` endpoint
   - Add proper error handling and response validation

2. **Update API dependencies**
   - Modify `backend/app/api/dependencies.py` to include POI analyzer
   - Ensure POI analyzer is available in dependency injection
   - Add POI analyzer to existing analyzer factory integration

3. **Console investigation command**
   - Add `investigate` command to `console/app/main.py`
   - Support target time specification (datetime string)
   - Support position filtering and window configuration
   - Call multiple POI API endpoints and collate results
   - Generate formatted report output for manual verification

4. **Console report formatting**
   - Create rich tables for investigation context display
   - Format timeline events with associated markers hierarchically
   - Display resource snapshots in readable format
   - Show event bursts with intensity metrics
   - Include relevance scores and sorting
   - Handle partial results when individual APIs fail

**Success Criteria**:
- API endpoints return properly formatted JSON responses
- Console command generates comprehensive investigation reports
- Manual verification possible against test data
- Error handling works for edge cases

### Task 7: Integration and Testing
**Duration**: 2-3 days
**Dependencies**: All previous tasks complete

**Subtasks**:
1. **Comprehensive unit tests**
   - Test each method with various input scenarios
   - Test error handling and edge cases
   - Test integration with existing analyzers
   - Achieve >90% code coverage

2. **Integration tests with real data**
   - Test with actual log files
   - Validate investigation context completeness
   - Test preset execution with real scenarios
   - Verify performance with larger datasets

3. **API documentation**
   - Document all public methods and parameters
   - Provide usage examples
   - Document return data structures
   - Include troubleshooting guide

4. **Performance baseline establishment**
   - Measure response times for various scenarios
   - Identify performance bottlenecks
   - Document acceptable performance ranges
   - Plan optimization strategies for Phase 2

**Success Criteria**:
- All tests pass consistently
- Integration with existing analyzers works smoothly
- Documentation is complete and accurate
- Performance meets basic requirements

## Technical Specifications

### File Structure
```
log_parser/log_parser/analysis/
├── points_of_interest_analyzer.py    # Main POI analyzer class
├── __init__.py                       # Updated to include POI analyzer
└── analyzer_factory.py               # Updated to support POI analyzer

backend/app/api/routes/
├── points_of_interest.py             # POI API endpoints
└── __init__.py                       # Updated to include POI routes

console/app/
└── main.py                           # Updated with investigate command

log_parser/tests/
├── test_points_of_interest_analyzer.py    # Unit tests
└── integration/
    └── test_poi_integration.py            # Integration tests
```

### Key Method Signatures
```python
class PointsOfInterestAnalyzer:
    def __init__(self, structured_analyzer, resource_analyzer, timeline_analyzer, cross_analyzer)
    def get_investigation_context(self, target_time, window_minutes=60, position=None) -> dict
    def detect_event_bursts(self, window_minutes=5, threshold_count=3, event_types=None, start_time=None, end_time=None) -> list
    def calculate_relevance_scores(self, events, investigation_context) -> list
```

### API Endpoints
```python
# GET /poi/investigation-context
# Query params: target_time (ISO), window_minutes (int), position (str, optional)
# Returns: Investigation context with all relevant data

# GET /poi/event-bursts  
# Query params: window_minutes (int), threshold_count (int), start_time (ISO), end_time (ISO), position (str, optional)
# Returns: List of detected event bursts
```

### Console Commands
```bash
# Basic investigation around specific time
tern investigate --target-time "2023-01-01T14:30:00" --window-minutes 60

# Investigation with position filter
tern investigate --target-time "2023-01-01T14:30:00" --position "PAE12345" --window-minutes 120

# Comprehensive report (calls both APIs and collates)
tern investigate --target-time "2023-01-01T14:30:00" --comprehensive

# Event burst detection for time range
tern investigate --start-time "2023-01-01T10:00:00" --end-time "2023-01-01T16:00:00" --bursts-only
```

### Data Structures
```python
# Investigation Context Structure
{
    'target_time': datetime,
    'window': {'start': datetime, 'end': datetime},
    'position': str | None,  # Position filter if specified
    'critical_events': [  # ERROR/WARNING events within investigation window
        {
            'timestamp': datetime,
            'log_level': str,
            'log_event': str,
            'relevance_score': float,
            'message': str
        }
    ],
    'active_timelines': [  # Timeline events active during target time (may span beyond window)
        {
            'timeline_event': {
                'id': str,
                'name': str,
                'start_time': datetime,
                'end_time': datetime | None,
                'position_id': str,
                'metadata': dict
            },
            'relevant_markers': [  # Markers within window that belong to this timeline event
                {
                    'id': str,
                    'name': str,
                    'timestamp': datetime,
                    'metadata': dict
                }
            ]
        }
    ],
    'resource_snapshot': {
        'cpu_usage_percent': float,
        'memory_usage_percent': float,
        'disk_usage_percent': float,
        'timestamp': datetime
    },
    'concurrent_activity': [  # Events across other positions at same time
        {
            'position': str,
            'events': [
                {
                    'timestamp': datetime,
                    'log_event': str,
                    'log_level': str
                }
            ]
        }
    ],
    'recent_anomalies': [  # Statistical anomalies within expanded window
        {
            'type': str,  # 'resource_spike', 'log_burst', etc.
            'column': str,
            'start_time': datetime,
            'value': float,
            'threshold': float,
            'severity': str
        }
    ]
}

# Event Burst Structure
{
    'start_time': datetime,
    'end_time': datetime,
    'duration_minutes': float,
    'event_count': int,
    'events_per_minute': float,
    'event_types': [...],
    'affected_positions': [...],
    'peak_intensity': float
}
```

### Error Handling Strategy
- Graceful degradation when analyzer data is unavailable
- Comprehensive logging for debugging
- Meaningful error messages for API consumers
- Fallback behavior when specific analyzers fail

## Dependencies and Prerequisites

### Required Existing Components
- StructuredLogAnalyzer with time-based querying
- ResourceAnalyzer with system overview capabilities
- TimelineAnalyzer with event and marker querying
- CrossAnalyzer for multi-source coordination

### Required Development Tools
- Python 3.10+ with typing support
- pytest for testing framework
- pandas for data manipulation
- datetime for time calculations

### Test Data Requirements
- Sample log files with error events
- Resource usage data with identifiable peaks
- Timeline events with various completion states
- Multi-position log data for concurrent activity testing

## Risk Mitigation

### Technical Risks
- **Analyzer dependency failures**: Implement fallback behavior and comprehensive error handling
- **Performance with large datasets**: Establish performance monitoring and optimization checkpoints
- **Data consistency issues**: Validate data from existing analyzers before processing

### Implementation Risks
- **Scope creep**: Strictly limit Phase 1 to core orchestration capabilities
- **Over-engineering**: Focus on simple, working implementations over complex algorithms
- **Integration complexity**: Test with existing analyzers early and often

### Timeline Risks
- **Underestimated complexity**: Add 20% buffer to all task estimates
- **Dependency delays**: Parallelize tasks where possible
- **Testing overhead**: Allocate sufficient time for thorough testing

## Success Metrics

### Functional Metrics
- **Investigation Context Completeness**: >90% of relevant events captured
- **Burst Detection Accuracy**: Correctly identifies known event bursts
- **Preset Execution Success**: All presets execute without errors
- **API Reliability**: <1% error rate in normal operation

### Performance Metrics
- **Investigation Context Response Time**: <2 seconds for typical scenarios
- **Burst Detection Performance**: <5 seconds for 24-hour analysis
- **Memory Usage**: <500MB for typical investigation scenarios
- **Test Coverage**: >90% code coverage

### Quality Metrics
- **Unit Test Pass Rate**: 100% in CI/CD
- **Integration Test Stability**: >95% pass rate
- **Code Quality**: Pass linting and type checking
- **Documentation Completeness**: All public methods documented

## Deliverables and Timeline

### Week 1
- **Days 1-2**: Task 1 - Core Infrastructure Setup
- **Days 3-5**: Task 2 - Investigation Context Builder (start)

### Week 2
- **Days 1-2**: Task 2 - Investigation Context Builder (complete)
- **Days 3-5**: Task 3 - Event Burst Detection

### Week 3
- **Days 1-3**: Task 4 - Simple Relevance Scoring
- **Days 4-5**: Task 5 - Investigation Presets (start)

### Week 4
- **Days 1-2**: Task 5 - Investigation Presets (complete)
- **Days 3-5**: Task 6 - API Routes and Console Integration

### Week 5
- **Days 1-3**: Task 7 - Integration and Testing
- **Days 4-5**: Documentation and final validation

### Final Deliverables
- Fully functional POI analyzer with core capabilities
- Comprehensive test suite with >90% coverage
- Complete API documentation
- Performance baseline measurements
- Integration guide for Phase 2 development

## Next Steps After Phase 1

### Phase 2 Planning
- Enhanced relevance scoring with anomaly integration
- Cross-position correlation algorithms
- Baseline comparison methods
- Performance optimization and caching

### User Feedback Integration
- Collect user feedback on investigation context usefulness
- Identify most valuable presets for enhancement
- Understand real-world investigation patterns
- Plan UI integration based on API usage

### Technical Debt Management
- Address any shortcuts taken during Phase 1
- Optimize performance bottlenecks identified
- Refactor based on actual usage patterns
- Plan architecture improvements for Phase 2