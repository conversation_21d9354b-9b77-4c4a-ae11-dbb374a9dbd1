"""Console application for interacting with the log visualizer API."""

from __future__ import annotations

import json
import re
import sys
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

import plotext as plt
import requests
import typer
from dateutil import parser
from rich.console import Console
from rich.progress import Progress
from rich.table import Table

console = Console()
app = typer.Typer()


class LogVisualizerClient:
    """Client for interacting with the Log Visualizer API."""

    def __init__(self, host: str = "http://localhost", port: int = 8000):
        """Initialize with host and port."""
        self.host = host
        self.port = port
        self.base_url = f"{host}:{port}"

        # Check if the server is available and inform about startup
        try:
            console.print(f"[dim]Connecting to Log Visualizer API at {self.base_url}...[/dim]")
            response = requests.get(f"{self.base_url}/health", timeout=10)

            # Check server initialization status
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "starting":
                    console.print("[yellow]The server is still initializing. First requests may be slower while data is cached.[/yellow]")

                    # Show more detailed initialization status if available
                    if "details" in data:
                        details = data["details"]
                        console.print("[yellow]Initialization status:[/yellow]")
                        for key, value in details.items():
                            status = "[green]✓" if value else "[red]✗"
                            console.print(f"  {status} {key.replace('_', ' ').title()}[/]")

                    console.print(
                        "[yellow]The server will be fully operational soon. You can proceed with queries, but some operations may take longer initially.[/yellow]"
                    )
                elif data.get("status") == "error":
                    console.print(f"[red]Warning: The server reported an error: {data.get('error', 'Unknown error')}[/red]")
                else:
                    console.print("[green]Successfully connected to Log Visualizer API[/green]")
            else:
                console.print(f"[yellow]Warning: Received unexpected status code {response.status_code} from the server.[/yellow]")
        except requests.RequestException:
            console.print("[yellow]Warning: Could not connect to the Log Visualizer API. Ensure the backend is running.[/yellow]")

    def get_timestamp_range(
        self, range_type: str = "common", position: str | None = None
    ) -> tuple[datetime | None, datetime | None, dict | None]:
        """Get timestamp range and column availability from the API."""
        try:
            params = {"range_type": range_type}
            if position:
                params["position"] = position

            response = requests.get(f"{self.base_url}/analysis/timestamp_range", params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            start = None
            end = None
            if data.get("start"):
                start = datetime.fromisoformat(data["start"].replace("Z", "+00:00"))
            if data.get("end"):
                end = datetime.fromisoformat(data["end"].replace("Z", "+00:00"))

            return start, end, data.get("column_availability")

        except requests.RequestException as e:
            console.print(f"[red]Error getting timestamp range: {e}[/red]")
            return None, None, None

    def get_resource_data(self, start: datetime, end: datetime, columns: list[str] | None = None, offset: int = 0) -> list[dict]:
        """Get resource data for the specified time range."""
        try:
            params = {
                "start": start.isoformat(),
                "end": end.isoformat(),
                "offset": offset,
            }
            if columns:
                params["columns"] = ",".join(columns)

            response = requests.get(f"{self.base_url}/resource", params=params, timeout=30)
            response.raise_for_status()
            return response.json()

        except requests.RequestException as e:
            console.print(f"[red]Error getting resource data: {e}[/red]")
            return []

    def get_log_summary(
        self,
        start: str | None = None,
        end: str | None = None,
        positions: str | None = None,
    ) -> dict | None:
        """Get log summary data."""
        try:
            params = {}
            if start:
                params["start"] = start
            if end:
                params["end"] = end
            if positions:
                params["positions"] = positions

            response = requests.get(f"{self.base_url}/analysis/log_summary", params=params, timeout=10)
            response.raise_for_status()
            return response.json()

        except requests.RequestException as e:
            console.print(f"[red]Error getting log summary: {e}[/red]")
            return None

    def get_timeline_events(
        self,
        start: str | None = None,
        end: str | None = None,
        position_id: str | None = None,
        timeline_event_type: str | None = None,
    ) -> list[dict]:
        """Get timeline events with optional filters."""
        params = {}
        if start:
            params["start"] = start
        if end:
            params["end"] = end
        if position_id:
            params["position_id"] = position_id
        if timeline_event_type:
            params["timeline_event_type"] = timeline_event_type

        try:
            response = requests.get(f"{self.base_url}/timeline", params=params, timeout=30)
            response.raise_for_status()
            timeline_events = response.json()["timeline_events"]
            return timeline_events
        except Exception as e:
            console.print(f"[red]Error getting timeline events: {e}[/red]")
            return []

    def get_timeline_config(self) -> dict:
        """Get timeline event configuration."""
        try:
            response = requests.get(f"{self.base_url}/timeline/config", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            console.print(f"[red]Error getting timeline event configuration: {e}[/red]")
            return {}

    def get_timeline_event_types(self) -> list[str]:
        """Get available timeline event types from the API."""
        config = self.get_timeline_config()
        timeline_event_types = []
        if config and "timeline_event_types" in config:
            timeline_event_types = list(config["timeline_event_types"].keys())
        return timeline_event_types

    def get_marker_types(self) -> list[str]:
        """Get available marker types from the API."""
        config = self.get_timeline_config()
        marker_types = []
        if config and "marker_types" in config:
            marker_types = list(config["marker_types"].keys())
        return marker_types

    def get_timeline_markers(
        self,
        marker_type: str | None = None,
        timeline_event_id: str | None = None,
        position_id: str | None = None,
        start: str | None = None,
        end: str | None = None,
    ) -> list[dict]:
        """Get timeline markers with optional filters."""
        params = {}
        if marker_type:
            params["marker_type"] = marker_type
        if timeline_event_id:
            params["timeline_event_id"] = timeline_event_id
        if position_id:
            params["position_id"] = position_id
        if start:
            params["start"] = start
        if end:
            params["end"] = end

        try:
            response = requests.get(f"{self.base_url}/timeline/markers", params=params, timeout=30)
            response.raise_for_status()
            markers = response.json()["markers"]
            return markers
        except Exception as e:
            console.print(f"[red]Error getting timeline markers: {e}[/red]")
            return []

    def get_investigation_context(
        self, target_time: str, window_minutes: int = 60, position: str | None = None
    ) -> dict:
        """Get comprehensive investigation context around a specific time."""
        try:
            params = {
                "target_time": target_time,
                "window_minutes": window_minutes,
            }
            if position:
                params["position"] = position

            response = requests.get(f"{self.base_url}/poi/investigation_context", params=params, timeout=30)
            response.raise_for_status()
            return response.json()

        except requests.RequestException as e:
            console.print(f"[red]Error getting investigation context: {e}[/red]")
            return {}

    def detect_event_bursts(
        self, 
        window_minutes: int = 5, 
        threshold_count: int = 3,
        start_time: str | None = None,
        end_time: str | None = None,
        position: str | None = None
    ) -> list:
        """Detect rapid increases in event counts."""
        try:
            params = {
                "window_minutes": window_minutes,
                "threshold_count": threshold_count,
            }
            if start_time:
                params["start_time"] = start_time
            if end_time:
                params["end_time"] = end_time
            if position:
                params["position"] = position

            response = requests.get(f"{self.base_url}/poi/event_bursts", params=params, timeout=30)
            response.raise_for_status()
            return response.json()

        except requests.RequestException as e:
            console.print(f"[red]Error detecting event bursts: {e}[/red]")
            return []

    def get_full_timestamp_range(self) -> tuple[datetime | None, datetime | None]:
        """Get the full timestamp range from the resource DataFrame."""
        if hasattr(self, "resource_df") and not self.resource_df.empty:
            resource_min = self.resource_df.index.min()
            resource_max = self.resource_df.index.max()
            return round_down_to_minute(resource_min), round_up_to_minute(resource_max)
        console.print("[red]Error: Resource DataFrame is empty or not initialized.[/red]")
        return None, None

    def get_available_columns(self) -> dict:
        """Get list of available resource columns and their statistics from the API."""
        try:
            response = requests.get(f"{self.base_url}/resource/columns", timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            console.print(f"[red]Error getting available columns: {e}[/red]")
            # Return empty defaults that match the expected structure
            return {"columns": [], "column_stats": {}}

    def get_logs(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        position: str | None = None,
        limit: int = 1000,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        """Get structured logs for the specified time range with pagination.

        Args:
            start: Start time for filtering logs
            end: End time for filtering logs
            position: Filter logs by position
            limit: Maximum number of logs to return per request
            offset: Offset for pagination

        Returns:
            List of log entries as dictionaries
        """
        try:
            params = {"limit": limit, "offset": offset}

            if start:
                params["start"] = start.isoformat()
            if end:
                params["end"] = end.isoformat()
            if position:
                params["positions"] = position

            response = requests.get(f"{self.base_url}/minknow", params=params, timeout=30)
            response.raise_for_status()
            data = response.json()

            return data.get("entries", [])
        except requests.RequestException as e:
            console.print(f"[red]Error getting logs: {e}[/red]")
            return []

    def detect_log_level_anomalies(
        self,
        start: datetime | None = None,
        end: datetime | None = None,
        position: str | None = None,
        error_threshold: float = 0.05,
        warning_threshold: float = 0.40,
        window_size: str = "10min",
    ) -> list[dict[str, Any]]:
        """Detect anomalies in log levels based on error and warning rate thresholds.

        Args:
            start: Start time for analysis
            end: End time for analysis
            position: Filter by position
            error_threshold: Percentage threshold for error rate (0.0 to 1.0)
            warning_threshold: Percentage threshold for warning rate (0.0 to 1.0)
            window_size: Time window size for analysis (pandas frequency string)

        Returns:
            List of dictionaries containing anomaly information
        """
        try:
            # Build query parameters
            params = {
                "start": start.isoformat() if start else None,
                "end": end.isoformat() if end else None,
                "position": position,
                "error_threshold": error_threshold,
                "warning_threshold": warning_threshold,
                "window_size": window_size,
            }

            # Remove None values
            params = {k: v for k, v in params.items() if v is not None}

            # Make API request
            response = requests.get(f"{self.base_url}/analysis/log_level_anomalies", params=params, timeout=30)
            response.raise_for_status()

            return response.json()
        except requests.RequestException as e:
            console.print(f"[red]Error detecting log level anomalies: {e}[/red]")
            return []

    def detect_time_series_anomalies(
        self,
        column: str,
        method: str = "zscore",
        start: datetime | None = None,
        end: datetime | None = None,
        position: str | None = None,
        window_size: str = "10min",
        zscore_threshold: float | None = None,
        iqr_factor: float | None = None,
        control_chart_std: float | None = None,
    ) -> list[dict[str, Any]]:
        """Detect anomalies in time series data using various statistical methods.

        Args:
            column: Column to analyze (e.g., 'cpu_percent', 'memory_percent')
            method: Anomaly detection method to use:
                   - 'zscore': Z-score based detection (default)
                   - 'iqr': Interquartile Range based detection
                   - 'control_chart': Control chart style detection
                   - 'persist': Persistence-based detection using ADTK
                   - 'level_shift': Level shift detection using ADTK
            start: Start time for analysis
            end: End time for analysis
            position: Filter by position
            window_size: Time window size for resampling (pandas frequency string)
            zscore_threshold: Threshold for z-score method
            iqr_factor: Factor for IQR method
            control_chart_std: Number of standard deviations for control chart

        Returns:
            List of dictionaries containing anomaly information
        """
        try:
            # Build query parameters
            params = {
                "column": column,
                "method": method,
                "start": start.isoformat() if start else None,
                "end": end.isoformat() if end else None,
                "position": position,
                "window_size": window_size,
            }

            # Add method-specific parameters
            if method == "zscore" and zscore_threshold is not None:
                params["zscore_threshold"] = zscore_threshold
            elif method == "iqr" and iqr_factor is not None:
                params["iqr_factor"] = iqr_factor
            elif method == "control_chart" and control_chart_std is not None:
                params["control_chart_std"] = control_chart_std

            # Remove None values
            params = {k: v for k, v in params.items() if v is not None}

            # Make API request
            response = requests.get(f"{self.base_url}/analysis/time_series_anomalies", params=params, timeout=30)

            # Check for specific error status codes
            if response.status_code == 404:
                console.print(f"[red]Error: Column '{column}' not found on the server.[/red]")
                return []
            if response.status_code == 400:
                error_data = response.json()
                error_message = error_data.get("detail", "Unknown error")
                console.print(f"[red]Error: {error_message}[/red]")
                return []

            response.raise_for_status()

            return response.json()
        except requests.RequestException as e:
            console.print(f"[red]Error detecting time series anomalies: {e}[/red]")
            return []

    def get_positions(self) -> list[str]:
        """Get all available positions."""
        try:
            # Get log summary data which contains position information
            response = requests.get(f"{self.base_url}/analysis/log_summary", timeout=10)
            response.raise_for_status()
            log_summary = response.json()

            # Extract positions from log summary
            positions = []
            if log_summary and "counts_by_position" in log_summary:
                positions = list(log_summary["counts_by_position"].keys())

            return positions
        except requests.RequestException as e:
            console.print(f"[red]Error getting positions: {e}[/red]")
            return []

    def get_action_types(self) -> list[str]:
        """Get all available action types."""
        try:
            # Get action configuration which contains action types
            config = self.get_action_config()

            # Extract action types from config
            action_types = []
            if config and "action_types" in config:
                action_types = list(config["action_types"].keys())

            return action_types
        except Exception as e:
            console.print(f"[red]Error getting action types: {e}[/red]")
            return []


def plot_cpu_load(data: list[dict]) -> None:
    """Plot CPU load data using plotext with a dark theme."""
    if not data:
        console.print("[yellow]No data available to plot[/yellow]")
        return

    # Extract and convert datetimes to float timestamps
    timestamps = []
    load1_values = []
    load5_values = []
    load15_values = []
    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)
        load1_values.append(record.get("Load1", 0))
        load5_values.append(record.get("Load5", 0))
        load15_values.append(record.get("Load15", 0))
    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()

    # Use the built-in dark theme
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]  # Get the width of the console
    console_height = console.size[1]  # Get the height of the console
    plot_height = console_height // 2  # Set plot height to half of console height

    # Set the plot size to full width and half height
    plt.plot_size(console_width, plot_height)

    # Plot the data with different markers for better visibility
    plt.plot(x_values, load1_values, label="Load1", marker="braille", color="red")
    plt.plot(x_values, load5_values, label="Load5", marker="braille", color="yellow")
    plt.plot(x_values, load15_values, label="Load15", marker="braille", color="green")

    # Dynamically set x-axis labels as times
    num_data_points = len(x_values)
    max_ticks = console_width // 10  # Adjust this divisor based on desired tick spacing
    num_ticks = min(max_ticks, num_data_points)  # Limit ticks to console width and available data points
    step = max(1, num_data_points // num_ticks)

    # Set x-ticks to show evenly spaced timestamps
    plt.xticks(x_values[::step], [ts.strftime("%Y-%m-%d %H:%M") for ts in timestamps[::step]])

    # Set y-axis limits with some padding
    max_load = max(max(load1_values), max(load5_values), max(load15_values))
    plt.ylim(0, max_load * 1.1)  # Add 10% padding to the top

    plt.title("CPU Load Over Time")
    plt.xlabel("Time")
    plt.ylabel("Load Average")
    plt.grid(True)

    plt.show()


def plot_memory_usage(data: list[dict]) -> None:
    """Plot memory usage data using plotext with a dark theme."""
    if not data:
        console.print("[yellow]No data available to plot[/yellow]")
        return

    # Extract and convert datetimes to float timestamps
    timestamps = []
    total_values = []
    used_values = []
    available_values = []
    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)
        total = record.get("MemTotalBytes", 0) / (1024 * 1024 * 1024)  # Convert to GB
        available = record.get("MemAvailableBytes", 0) / (1024 * 1024 * 1024)  # Convert to GB
        used = record.get("MemUsedBytes", 0) / (1024 * 1024 * 1024)  # Convert to GB
        total_values.append(total)
        used_values.append(used)
        available_values.append(available)
    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()

    # Use the built-in dark theme
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 2

    # Set the plot size to full width and half height
    plt.plot_size(console_width, plot_height)

    # Plot the data
    plt.plot(x_values, total_values, label="Total Memory (GB)", marker="braille")
    plt.plot(x_values, used_values, label="Used Memory (GB)", marker="braille")
    plt.plot(x_values, available_values, label="Available Memory (GB)", marker="braille")

    # Dynamically set x-axis labels as times
    num_data_points = len(x_values)
    max_ticks = console_width // 10
    num_ticks = min(max_ticks, num_data_points)
    step = max(1, num_data_points // num_ticks)

    plt.xticks(x_values[::step], [ts.strftime("%Y-%m-%d %H:%M") for ts in timestamps[::step]])

    plt.title("Memory Usage Over Time")
    plt.xlabel("Time")
    plt.ylabel("Memory (GB)")
    plt.grid(True)

    plt.show()


def plot_disk_usage(data: list[dict]) -> None:
    """Plot disk usage data using plotext with a dark theme."""
    if not data:
        console.print("[yellow]No data available to plot[/yellow]")
        return

    # Extract and convert datetimes to float timestamps
    timestamps = []
    disk_data = {}  # Dictionary to store usage for each disk

    # First pass: discover block devices from column names
    block_devices = set()
    for record in data:
        for key in record.keys():
            if key.endswith("_read") or key.endswith("_write"):
                device_name = key.rsplit("_", 1)[0]  # Remove _read or _write suffix
                block_devices.add(device_name)

    # Initialize disk_data dictionary
    for device in block_devices:
        disk_data[device] = {"total": [], "free": [], "used": []}

    # Collect data
    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)

        # For each block device, get its usage data
        for device in block_devices:
            read_key = f"{device}_read"
            write_key = f"{device}_write"

            if read_key in record and write_key in record:
                read_rate = record[read_key] / (1024 * 1024) if record[read_key] is not None else 0  # Convert to MB/s
                write_rate = record[write_key] / (1024 * 1024) if record[write_key] is not None else 0  # Convert to MB/s

                disk_data[device]["total"].append(read_rate + write_rate)
                disk_data[device]["used"].append(write_rate)
                disk_data[device]["free"].append(read_rate)

    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 2

    plt.plot_size(console_width, plot_height)

    # Plot data for each disk
    for device, data in disk_data.items():
        plt.plot(x_values, data["total"], label=f"{device} Total (MB/s)", marker="braille")
        plt.plot(x_values, data["used"], label=f"{device} Write (MB/s)", marker="braille")
        plt.plot(x_values, data["free"], label=f"{device} Read (MB/s)", marker="braille")

    # Dynamically set x-axis labels as times
    num_data_points = len(x_values)
    max_ticks = console_width // 10
    num_ticks = min(max_ticks, num_data_points)
    step = max(1, num_data_points // num_ticks)

    plt.xticks(x_values[::step], [ts.strftime("%Y-%m-%d %H:%M") for ts in timestamps[::step]])

    plt.title("Disk I/O Usage Over Time")
    plt.xlabel("Time")
    plt.ylabel("MB/s")
    plt.grid(True)

    plt.show()


def plot_memory_percent(data: list[dict]) -> None:
    """Plot memory usage percentage over time."""
    if not data:
        console.print("[yellow]No data available to plot[/yellow]")
        return

    # Extract and convert datetimes to float timestamps
    timestamps = []
    mem_percent = []
    swap_percent = []
    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)
        mem_percent.append(record.get("MemUsedPercent", 0))
        swap_percent.append(record.get("SwapUsedPercent", 0))
    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()

    # Use the built-in dark theme
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 2

    # Set the plot size to full width and half height
    plt.plot_size(console_width, plot_height)

    # Plot the data
    plt.plot(x_values, mem_percent, label="Memory Used %", marker="braille")
    plt.plot(x_values, swap_percent, label="Swap Used %", marker="braille")

    # Dynamically set x-axis labels as times
    num_data_points = len(x_values)
    max_ticks = console_width // 10
    num_ticks = min(max_ticks, num_data_points)
    step = max(1, num_data_points // num_ticks)

    # Set x-ticks to show evenly spaced timestamps
    plt.xticks(x_values[::step], [ts.strftime("%Y-%m-%d %H:%M") for ts in timestamps[::step]])

    plt.title("Memory Usage Percentage Over Time")
    plt.xlabel("Time")
    plt.ylabel("Usage %")
    plt.grid(True)

    plt.show()


def plot_disk_free_bytes(data: list[dict]) -> None:
    """Plot free bytes for all disk mounts over time."""
    if not data:
        console.print("[yellow]No data available to plot[/yellow]")
        return

    # Extract and convert datetimes to float timestamps
    timestamps = []
    disk_free_bytes = {}  # Dictionary to store free bytes for each disk

    # First pass: discover block devices from column names
    block_devices = set()
    for record in data:
        for key in record.keys():
            if key.endswith("_read") or key.endswith("_write"):
                device_name = key.rsplit("_", 1)[0]  # Remove _read or _write suffix
                block_devices.add(device_name)

    # Initialize disk_free_bytes dictionary
    for device in block_devices:
        disk_free_bytes[device] = []

    # Collect data
    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)

        # For each block device, get its free bytes
        for device in block_devices:
            read_key = f"{device}_read"
            write_key = f"{device}_write"

            if read_key in record and write_key in record:
                read_rate = record[read_key] / (1024 * 1024) if record[read_key] is not None else 0  # Convert to MB/s
                disk_free_bytes[device].append(read_rate)

    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 2

    plt.plot_size(console_width, plot_height)

    # Plot data for each disk
    for device, free_bytes in disk_free_bytes.items():
        plt.plot(x_values, free_bytes, label=f"{device} Read Rate (MB/s)", marker="braille")

    # Dynamically set x-axis labels as times
    num_data_points = len(x_values)
    max_ticks = console_width // 10
    num_ticks = min(max_ticks, num_data_points)
    step = max(1, num_data_points // num_ticks)

    plt.xticks(x_values[::step], [ts.strftime("%Y-%m-%d %H:%M") for ts in timestamps[::step]])

    plt.title("Disk Read Rates Over Time")
    plt.xlabel("Time")
    plt.ylabel("MB/s")
    plt.grid(True)

    plt.show()


def plot_gpu_utilization(data: list[dict]) -> None:
    """Plot GPU utilization over time."""
    timestamps = []
    utilization = []

    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)
        utilization.append(record.get("UtilizationPercent", 0))

    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 4  # Set plot height to a quarter of console height

    plt.plot_size(console_width, plot_height)
    plt.plot(x_values, utilization, label="GPU Utilization %", color="blue", marker="braille")
    plt.title("GPU Utilization Over Time")
    plt.xlabel("Time")
    plt.ylabel("Utilization %")
    plt.grid(True)
    plt.show()


def plot_gpu_power(data: list[dict]) -> None:
    """Plot GPU power draw over time."""
    timestamps = []
    power = []

    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)
        power.append(record.get("PowerDrawWatts", 0))

    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 4  # Set plot height to a quarter of console height

    plt.plot_size(console_width, plot_height)
    plt.plot(x_values, power, label="Power Draw (W)", color="purple", marker="braille")
    plt.title("GPU Power Draw Over Time")
    plt.xlabel("Time")
    plt.ylabel("Power (W)")
    plt.grid(True)
    plt.show()


def plot_gpu_temperature(data: list[dict]) -> None:
    """Plot GPU temperature over time."""
    timestamps = []
    temperature = []

    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)
        temperature.append(record.get("Temperature", 0))

    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 4  # Set plot height to a quarter of console height

    plt.plot_size(console_width, plot_height)
    plt.plot(x_values, temperature, label="Temperature (°C)", color="red", marker="braille")
    plt.title("GPU Temperature Over Time")
    plt.xlabel("Time")
    plt.ylabel("Temperature (°C)")
    plt.grid(True)
    plt.show()


def plot_gpu_memory(data: list[dict]) -> None:
    """Plot GPU memory usage percentage over time."""
    timestamps = []
    memory_percent = []

    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)
        total_mb = record.get("MemoryTotalMB", 0)
        free_mb = record.get("MemoryFreeMB", 0)
        if total_mb > 0:
            memory_percent.append(((total_mb - free_mb) / total_mb) * 100)
        else:
            memory_percent.append(0)

    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 4  # Set plot height to a quarter of console height

    plt.plot_size(console_width, plot_height)
    plt.plot(x_values, memory_percent, label="Memory Usage %", color="green", marker="braille")
    plt.title("GPU Memory Usage Over Time")
    plt.xlabel("Time")
    plt.ylabel("Memory Usage %")
    plt.grid(True)
    plt.show()


def plot_block_io(data: list[dict]) -> None:
    """Plot block device I/O rates over time."""
    if not data:
        console.print("[yellow]No data available to plot[/yellow]")
        return

    # First pass: discover block devices from column names
    block_devices = set()
    for record in data:
        # The Block field should now contain flattened data
        if isinstance(record.get("Block"), dict):
            for key in record["Block"].keys():
                if key.endswith("_read"):
                    device_name = key[:-5]  # Remove _read suffix
                    block_devices.add(device_name)

    # Collect statistics for each device
    device_stats = {}
    for device in block_devices:
        device_stats[device] = {
            "total_records": 0,
            "non_zero_io": 0,
            "max_read": 0,
            "max_write": 0,
            "read_values": [],
            "write_values": [],
            "timestamps": [],
        }

    # Process data for each device
    for record in data:
        timestamp = parser.parse(record["timestamp"])
        block_data = record.get("Block", {})

        for device in block_devices:
            read_key = f"{device}_read"
            write_key = f"{device}_write"

            if read_key in block_data and write_key in block_data:
                read_rate = block_data[read_key] / (1024 * 1024) if block_data[read_key] is not None else 0  # Convert to MB/s
                write_rate = block_data[write_key] / (1024 * 1024) if block_data[write_key] is not None else 0  # Convert to MB/s

                stats = device_stats[device]
                stats["total_records"] += 1
                if read_rate > 0 or write_rate > 0:
                    stats["non_zero_io"] += 1
                stats["max_read"] = max(stats["max_read"], read_rate)
                stats["max_write"] = max(stats["max_write"], write_rate)
                stats["read_values"].append(read_rate)
                stats["write_values"].append(write_rate)
                stats["timestamps"].append(timestamp)

    # Print statistics for each device
    for device, stats in device_stats.items():
        if stats["total_records"] == 0:
            continue

    if not any(stats["non_zero_io"] > 0 for stats in device_stats.values()):
        console.print("[yellow]No block devices found with I/O activity[/yellow]")
        return

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 2

    plt.plot_size(console_width, plot_height)

    # Plot data for each block device
    for device, stats in device_stats.items():
        if stats["timestamps"]:  # Only plot if we have data points
            x_values = [t.timestamp() for t in stats["timestamps"]]
            plt.plot(x_values, stats["read_values"], label=f"{device} Read MB/s", marker="braille")
            plt.plot(x_values, stats["write_values"], label=f"{device} Write MB/s", marker="braille")

    # Dynamically set x-axis labels as times
    if device_stats:
        # Get all timestamps from all devices
        all_timestamps = []
        for stats in device_stats.values():
            all_timestamps.extend(stats["timestamps"])
        all_timestamps = sorted(set(all_timestamps))  # Remove duplicates and sort

        x_values = [t.timestamp() for t in all_timestamps]
        num_data_points = len(x_values)
        max_ticks = console_width // 10
        num_ticks = min(max_ticks, num_data_points)
        step = max(1, num_data_points // num_ticks)

        plt.xticks(x_values[::step], [t.strftime("%Y-%m-%d %H:%M") for t in all_timestamps[::step]])

    plt.title("Block Device I/O Rates Over Time")
    plt.xlabel("Time")
    plt.ylabel("MB/s")
    plt.grid(True)

    plt.show()


def plot_disk_saturation(data: list[dict]) -> None:
    """Plot disk saturation levels over time."""
    if not data:
        console.print("[yellow]No data available to plot[/yellow]")
        return

    timestamps = []
    block_data = {}  # Dictionary to store saturation for each block device

    # First pass: discover block devices from column names
    block_devices = set()
    for record in data:
        for key in record.keys():
            if key.endswith("_saturation"):
                device_name = key.rsplit("_", 1)[0]  # Remove _saturation suffix
                block_devices.add(device_name)

    # Initialize block_data dictionary
    for device in block_devices:
        block_data[device] = []

    # Collect data
    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)

        # For each block device, get its saturation
        for device in block_devices:
            saturation_key = f"{device}_saturation"
            if saturation_key in record:
                saturation = record[saturation_key] * 100 if record[saturation_key] is not None else 0  # Convert to percentage
                block_data[device].append(saturation)

    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 2

    plt.plot_size(console_width, plot_height)

    # Plot data for each block device
    for device, saturations in block_data.items():
        plt.plot(x_values, saturations, label=f"{device} Saturation %", marker="braille")

    # Dynamically set x-axis labels as times
    num_data_points = len(x_values)
    max_ticks = console_width // 10
    num_ticks = min(max_ticks, num_data_points)
    step = max(1, num_data_points // num_ticks)

    plt.xticks(x_values[::step], [ts.strftime("%Y-%m-%d %H:%M") for ts in timestamps[::step]])

    plt.title("Disk Saturation Over Time")
    plt.xlabel("Time")
    plt.ylabel("Saturation %")
    plt.grid(True)

    plt.show()


def plot_swap_usage(data: list[dict]) -> None:
    """Plot swap usage over time."""
    if not data:
        console.print("[yellow]No data available to plot[/yellow]")
        return

    timestamps = []
    swap_total = []
    swap_used = []
    swap_percent = []

    for record in data:
        dt = parser.parse(record["timestamp"])
        timestamps.append(dt)
        swap_total.append(record.get("SwapTotalBytes", 0) / (1024 * 1024 * 1024))  # Convert to GB
        swap_used.append(record.get("SwapUsedBytes", 0) / (1024 * 1024 * 1024))  # Convert to GB
        swap_percent.append(record.get("SwapUsedPercent", 0))

    x_values = [t.timestamp() for t in timestamps]

    plt.clear_figure()
    plt.clear_data()
    plt.theme("dark")

    # Get console size
    console_width = console.size[0]
    console_height = console.size[1]
    plot_height = console_height // 2

    plt.plot_size(console_width, plot_height)

    # Plot both absolute values and percentage
    plt.plot(x_values, swap_total, label="Total Swap (GB)", marker="braille")
    plt.plot(x_values, swap_used, label="Used Swap (GB)", marker="braille")
    plt.plot(x_values, swap_percent, label="Swap Used %", marker="braille")

    # Dynamically set x-axis labels as times
    num_data_points = len(x_values)
    max_ticks = console_width // 10
    num_ticks = min(max_ticks, num_data_points)
    step = max(1, num_data_points // num_ticks)

    plt.xticks(x_values[::step], [ts.strftime("%Y-%m-%d %H:%M") for ts in timestamps[::step]])

    plt.title("Swap Usage Over Time")
    plt.xlabel("Time")
    plt.ylabel("GB / Percentage")
    plt.grid(True)

    plt.show()


def format_duration(seconds: float | None) -> str:
    """Format duration in seconds to HH:MM:SS format."""
    if seconds is None:
        return "incomplete"
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


def display_action_tables(client: LogVisualizerClient, position: str | None = None, action_type: str | None = None) -> None:
    """Display action data in tables grouped by position and action type."""
    # Get actions with optional filters
    actions = client.get_actions(position_id=position, action_type=action_type)

    if not actions:
        console.print("[yellow]No action data available[/yellow]")
        return

    # Group actions by position and action type
    grouped_actions = {}
    for action in actions:
        pos_id = action.get("position_id", "unknown")
        action_name = action.get("name", "unknown")

        if pos_id not in grouped_actions:
            grouped_actions[pos_id] = {}

        if action_name not in grouped_actions[pos_id]:
            grouped_actions[pos_id][action_name] = []

        grouped_actions[pos_id][action_name].append(action)

    # Display tables for each position and action type
    for pos_id, action_types in grouped_actions.items():
        console.print(f"\n[bold cyan]Position: {pos_id}[/bold cyan]")

        for action_name, actions_list in action_types.items():
            # Create table for this action type
            table = Table(title=f"[cyan]{action_name} Actions[/cyan]", show_lines=True)
            table.add_column("[bold]Start Time[/bold]")
            table.add_column("[bold]End Time[/bold]")
            table.add_column("[bold]Duration[/bold]")
            table.add_column("[bold]Action ID[/bold]")
            table.add_column("[bold]Log Count[/bold]")
            table.add_column("[bold]Error Count[/bold]")
            table.add_column("[bold]Info %[/bold]")
            table.add_column("[bold]Warning %[/bold]")
            table.add_column("[bold]Error %[/bold]")

            # Add rows for each action
            for action in actions_list:
                start_time = parser.parse(action.get("start_time", ""))
                end_time = parser.parse(action.get("end_time", ""))
                duration = action.get("duration", 0)

                # Get log summary for this action's time range and position
                try:
                    params = {
                        "start": start_time.isoformat(),
                        "end": end_time.isoformat(),
                        "position": pos_id,
                    }
                    response = requests.get(f"{client.base_url}/analysis/log_summary", params=params, timeout=10)
                    response.raise_for_status()
                    log_summary = response.json()

                    # Extract log summary data
                    total_logs = log_summary.get("total_entries", 0)
                    error_count = sum(log_summary.get("errors_by_position", {}).values())

                    # Calculate percentages for each log level
                    info_count = log_summary.get("counts_by_level", {}).get("INFO", 0)
                    warning_count = log_summary.get("counts_by_level", {}).get("WARNING", 0)
                    error_level_count = log_summary.get("counts_by_level", {}).get("ERROR", 0)

                    info_percent = (info_count / total_logs * 100) if total_logs > 0 else 0
                    warning_percent = (warning_count / total_logs * 100) if total_logs > 0 else 0
                    error_percent = (error_level_count / total_logs * 100) if total_logs > 0 else 0

                except (requests.RequestException, KeyError, ZeroDivisionError) as e:
                    console.print(f"[yellow]Warning: Could not get log summary for action {action.get('id', 'N/A')}: {e}[/yellow]")
                    total_logs = 0
                    error_count = 0
                    info_percent = 0
                    warning_percent = 0
                    error_percent = 0

                table.add_row(
                    f"[yellow]{start_time.strftime('%Y-%m-%d %H:%M:%S')}[/yellow]",
                    f"[yellow]{end_time.strftime('%Y-%m-%d %H:%M:%S')}[/yellow]",
                    f"[blue]{format_duration(duration)}[/blue]",
                    f"[green]{action.get('id', 'N/A')}[/green]",
                    f"[cyan]{total_logs}[/cyan]",
                    f"[red]{error_count}[/red]",
                    f"[green]{info_percent:.1f}%[/green]",
                    f"[yellow]{warning_percent:.1f}%[/yellow]",
                    f"[red]{error_percent:.1f}%[/red]",
                )

            console.print(table)


def list_available_filters(client: LogVisualizerClient) -> None:
    """List all available positions and action types."""
    # Get positions
    positions = client.get_positions()

    # Get action types
    action_types = client.get_action_types()

    # Display positions
    console.print("\n[bold cyan]Available Positions:[/bold cyan]")
    if positions:
        for position in sorted(positions):
            console.print(f"  [green]{position}[/green]")
    else:
        console.print("  [yellow]No positions available[/yellow]")

    # Display action types
    console.print("\n[bold cyan]Available Action Types:[/bold cyan]")
    if action_types:
        for action_type in sorted(action_types):
            console.print(f"  [green]{action_type}[/green]")
    else:
        console.print("  [yellow]No action types available[/yellow]")


@app.command()
def plot(
    host: str = typer.Option("http://localhost", help="API host URL"),
    port: int = typer.Option(8000, help="API port"),
    minutes: int | None = typer.Option(None, help="Number of minutes of data to plot"),
    plot_types: list[str] = typer.Option(None, help="Type(s) of plot(s) to generate, separated by spaces"),
    list_plots: bool = typer.Option(False, "--list", help="List all available plots"),
    range_type: str = typer.Option("common", help="Type of time range to use (common, resource, minknow, full)"),
    position: str | None = typer.Option(None, help="Position to plot data for"),
):
    """Plot resource data from the Log Visualizer API."""
    # Initialize plot_start, plot_end, and min_completeness
    plot_start = None
    plot_end = None
    min_completeness = 100  # Initialize to 100% for comparison

    # Define available plot types and their required columns
    plot_types_dict = {
        "cpu": {
            "name": "CPU Load",
            "description": "Shows system load averages over time (1, 5, and 15 minute averages)",
            "required": ["Load1", "Load5", "Load15"],
            "function": plot_cpu_load,
        },
        "memory": {
            "name": "Memory Usage (GB)",
            "description": "Shows total, used, and available memory in GB",
            "required": ["MemTotalBytes", "MemUsedBytes", "MemAvailableBytes"],
            "function": plot_memory_usage,
        },
        "memory-percent": {
            "name": "Memory Usage (%)",
            "description": "Shows memory and swap usage as percentages",
            "required": ["MemUsedPercent", "SwapUsedPercent"],
            "function": plot_memory_percent,
        },
        "disk": {
            "name": "Disk Usage",
            "description": "Shows total and used space for each disk mount",
            "required": ["Block"],  # We'll discover the actual columns dynamically
            "function": plot_disk_usage,
        },
        "disk-free": {
            "name": "Disk Free Space",
            "description": "Shows available space for each disk mount",
            "required": ["Block"],  # We'll discover the actual columns dynamically
            "function": plot_disk_free_bytes,
        },
        "gpu": {
            "name": "GPU Metrics",
            "description": "Shows GPU utilization, power, temperature, and memory usage",
            "required": [
                "UtilizationPercent",
                "PowerDrawWatts",
                "Temperature",
                "MemoryTotalMB",
                "MemoryFreeMB",
            ],
            "function": plot_gpu_utilization,
        },
        "block-io": {
            "name": "Block Device I/O",
            "description": "Shows read/write rates for block devices",
            "required": ["Block"],  # We'll discover the actual columns dynamically
            "function": plot_block_io,
        },
        "disk-saturation": {
            "name": "Disk Saturation",
            "description": "Shows disk saturation levels as percentages",
            "required": ["Block"],  # We'll discover the actual columns dynamically
            "function": plot_disk_saturation,
        },
        "swap": {
            "name": "Swap Usage",
            "description": "Shows swap space usage in GB and percentage",
            "required": ["SwapTotalBytes", "SwapUsedBytes", "SwapUsedPercent"],
            "function": plot_swap_usage,
        },
    }

    if list_plots:
        # List available plots
        console.print("\n[cyan]Available Plots:[/cyan]")

        # Get available columns and their statistics
        client = LogVisualizerClient(host, port)
        response = client.get_available_columns()
        if not response or not response.get("columns"):
            console.print("[red]Could not get column information from API. Please ensure the server is running and try again.[/red]")
            raise typer.Exit(code=1)

        available_columns = response["columns"]
        column_stats = response["column_stats"]

        for col in sorted(available_columns):
            stats = column_stats.get(col, {})

        for plot_id, plot_info in plot_types_dict.items():
            console.print(f"\n[green]{plot_id}[/green]: {plot_info['name']}")
            console.print(f"  {plot_info['description']}")
            console.print(f"  Required columns: {', '.join(plot_info['required'])}")

            # Check data coverage
            required_cols_stats = []
            for col in plot_info["required"]:
                if col in column_stats:
                    stats = column_stats[col]
                    required_cols_stats.append(
                        {
                            "column": col,
                            "completeness": stats["completeness"],
                            "first_seen": stats["first_seen"],
                            "last_seen": stats["last_seen"],
                        }
                    )

            if required_cols_stats:
                min_completeness = min(stat["completeness"] for stat in required_cols_stats)
                first_seen = min(stat["first_seen"] for stat in required_cols_stats)
                last_seen = max(stat["last_seen"] for stat in required_cols_stats)

                console.print(f"  Data Completeness: {min_completeness:.1f}%")
                console.print(f"  Date Range: {first_seen} to {last_seen}")
            else:
                console.print("  [red]No data coverage available.[/red]")

        return  # Exit after listing plots

    # Ensure plot_types is provided if not listing
    if plot_types is None:
        console.print("[red]Error: You must specify plot types unless using --list.[/red]")
        raise typer.Exit(code=1)

    client = LogVisualizerClient(host, port)

    # Get available columns and their statistics
    response = client.get_available_columns()
    if not response or not response.get("columns"):
        console.print("[red]Could not get column information from API. Please ensure the server is running and try again.[/red]")
        sys.exit(1)

    available_columns = response["columns"]
    column_stats = response["column_stats"]

    for col in sorted(available_columns):
        stats = column_stats.get(col, {})

    # Determine which plots are available based on columns and their completeness
    available_plots = {}
    for plot_id, plot_info in plot_types_dict.items():
        # Handle wildcard required columns
        required_cols = []
        for req_col in plot_info["required"]:
            if "*" in req_col:
                # Find all columns that match the pattern
                pattern = req_col.replace("*", ".*")
                matching_cols = [col for col in available_columns if re.match(pattern, col)]
                required_cols.extend(matching_cols)
            else:
                required_cols.append(req_col)

        # Check if all required columns are present and have sufficient data
        required_cols_stats = []
        for col in required_cols:
            col_found = False

            # Check in column_stats from get_available_columns()
            if col in column_stats:
                col_found = True
                stats = column_stats[col]
                required_cols_stats.append(
                    {
                        "column": col,
                        "completeness": stats.get("completeness", 0),
                        "first_seen": stats.get("first_seen", None),
                        "last_seen": stats.get("last_seen", None),
                    }
                )

            # If column not found, it's not available
            if not col_found:
                pass  # DEBUG message removed

        if len(required_cols_stats) == len(required_cols):  # All required columns found
            plot_info["column_stats"] = required_cols_stats
            plot_info["required"] = required_cols  # Update with expanded wildcard columns
            available_plots[plot_id] = plot_info

    if not available_plots:
        console.print("[red]No plots available - required columns not found in data[/red]")
        return

    # Get the data timestamp range regardless of whether minutes is specified
    plot_start, plot_end, column_availability = client.get_timestamp_range(range_type=range_type, position=position)

    # Apply minutes parameter after we have the full range
    if minutes is not None and minutes > 0 and plot_start is not None and plot_end is not None:
        original_end = plot_end
        adjusted_end = plot_start + timedelta(minutes=minutes)
        plot_end = min(adjusted_end, original_end)
        console.print(f"[cyan]Limiting to {minutes} minutes of data from {plot_start} to {plot_end}[/cyan]")

    # Check if plot_start and plot_end are None
    if plot_start is None or plot_end is None:
        console.print("[red]Error: Unable to determine the timestamp range. Please check the data source.[/red]")
        raise typer.Exit(code=1)

    console.print(f"[green]Using {range_type} range{' for position ' + position if position else ''}: {plot_start} to {plot_end}[/green]")

    # Check if we have column availability information
    if column_availability:
        # The structure might be in the new format with resource/minknow keys
        if "resource" in column_availability:
            column_details = column_availability["resource"]
            console.print("[green]Column availability information retrieved (new format)[/green]")
        # Or it might be in the old format with column_details
        elif "column_details" in column_availability:
            column_details = column_availability["column_details"]
            console.print("[green]Column availability information retrieved (old format)[/green]")
        else:
            column_details = {}
            console.print("[yellow]Warning: Unrecognized column availability format[/yellow]")
    else:
        column_details = {}
        console.print("[yellow]Warning: No column availability information available[/yellow]")

    # Show data availability information and handle time range selection
    for plot_type in plot_types:
        if plot_type in available_plots:
            plot_info = available_plots[plot_type]
            console.print(f"\n[cyan]Data Availability for {plot_info['name']}:[/cyan]")

            # Calculate min_completeness and get time range for this specific plot
            required_cols = plot_info["required"]
            required_cols_stats = []

            # Check each required column's availability
            if column_details:
                for col in required_cols:
                    if col in column_details:
                        stats = column_details[col]
                        # The structure can be different depending on the format
                        # New format has 'completeness', old format has 'availability_percent'
                        completeness = stats.get("completeness", stats.get("availability_percent", 0))
                        first_seen = stats.get("first_seen", None)
                        last_seen = stats.get("last_seen", None)
                        non_null_count = stats.get("count", stats.get("non_null_count", 0))
                        total_rows = stats.get("total_entries", stats.get("total_rows", 0))

                        required_cols_stats.append(
                            {
                                "column": col,
                                "completeness": completeness,
                                "first_seen": first_seen,
                                "last_seen": last_seen,
                                "non_null_count": non_null_count,
                                "total_rows": total_rows,
                            }
                        )
                    else:
                        console.print(f"[yellow]Warning: Column '{col}' not found in column availability data[/yellow]")
                        required_cols_stats.append(
                            {
                                "column": col,
                                "completeness": 0,
                                "first_seen": None,
                                "last_seen": None,
                                "non_null_count": 0,
                                "total_rows": 0,
                            }
                        )

            # Store the column stats in the plot_info for future use
            plot_info["column_stats"] = required_cols_stats

            if required_cols_stats:
                # Calculate minimum completeness percentage
                # Filter for columns that have some data
                valid_stats = [stat for stat in required_cols_stats if stat.get("completeness", 0) > 0]
                if valid_stats:
                    min_completeness = min(stat.get("completeness", 0) for stat in valid_stats)

                    # Find the common time range where all required columns have data
                    valid_first_seen = []
                    valid_last_seen = []

                    for stat in valid_stats:
                        if stat.get("first_seen"):
                            # Handle different date formats
                            if isinstance(stat["first_seen"], str):
                                valid_first_seen.append(parser.parse(stat["first_seen"]))
                            elif isinstance(stat["first_seen"], datetime):
                                valid_first_seen.append(stat["first_seen"])

                        if stat.get("last_seen"):
                            # Handle different date formats
                            if isinstance(stat["last_seen"], str):
                                valid_last_seen.append(parser.parse(stat["last_seen"]))
                            elif isinstance(stat["last_seen"], datetime):
                                valid_last_seen.append(stat["last_seen"])

                    # Only proceed if we have valid timestamps
                    if valid_first_seen and valid_last_seen:
                        plot_start_specific = max(valid_first_seen)
                        plot_end_specific = min(valid_last_seen)

                        # Make sure the range is valid
                        if plot_start_specific < plot_end_specific:
                            console.print(f"\n[green]Plot-specific time range: {plot_start_specific} to {plot_end_specific}[/green]")
                            console.print(f"[green]Overall time range: {plot_start} to {plot_end}[/green]")

                            # Use the plot-specific range if it's more restrictive
                            plot_start = max(plot_start, plot_start_specific)
                            plot_end = min(plot_end, plot_end_specific)

                        console.print(f"[green]Final time range for {plot_type}: {plot_start} to {plot_end}[/green]")

                    # Output data coverage information
                    row_counts = [stat.get("non_null_count", 0) for stat in valid_stats]
                    if row_counts:
                        console.print(f"[green]Data Coverage: {min_completeness:.1f}% complete[/green]")
                        console.print(f"[green]Rows with data: {min(row_counts):,}[/green]")
                    else:
                        console.print("[yellow]Data Coverage: Unknown[/yellow]")
                else:
                    console.print("[red]Warning: No data available for required columns.[/red]")
                    continue
            else:
                console.print("[red]No data coverage available.[/red]")
                continue

            all_data = []
            offset = 0

            with Progress() as progress:
                task = progress.add_task(f"[cyan]Fetching data for {plot_info['name']}...", total=None)  # Set total to None initially

                # Get the total rows from column stats if available
                total_rows = 0
                if required_cols_stats:
                    valid_stats = [stat for stat in required_cols_stats if stat["total_rows"] > 0]
                    if valid_stats:
                        total_rows = max(stat["total_rows"] for stat in valid_stats)
                        progress.update(task, total=total_rows)

                # Get the first batch of data
                required_columns = plot_info["required"]
                initial_data = client.get_resource_data(plot_start, plot_end, columns=required_columns, offset=0)

                if initial_data:
                    # For the first page, estimate total records
                    if total_rows > 0:
                        progress.update(task, total=total_rows)
                    else:
                        # Fallback to using column stats from global stats
                        column = required_columns[0]  # Use the first required column
                        if column in column_stats:
                            estimated_total = column_stats[column].get("total_entries", 0)
                            progress.update(task, total=estimated_total)

                    all_data.extend(initial_data)
                    offset += len(initial_data)
                    progress.update(task, completed=offset)

                # Continue fetching data until we get an empty response
                while True:
                    # Get the next batch of data
                    data = client.get_resource_data(plot_start, plot_end, columns=required_columns, offset=offset)
                    if not data or len(data) == 0:
                        break

                    all_data.extend(data)
                    offset += len(data)

                    # Update progress based on records fetched
                    progress.update(task, completed=offset)

            if not all_data:
                console.print(f"[red]No data available for the specified time range for {plot_info['name']}[/red]")
                continue

            console.print(f"[green]Plotting data for {plot_info['name']} from {plot_start} to {plot_end}[/green]")
            console.print(f"[green]Total data points: {len(all_data)}[/green]")

            # Call the appropriate plotting function
            plot_info["function"](all_data)


@app.command()
def data(
    summary: bool = typer.Option(False, "--summary", help="Show data summary"),
    plot: bool = typer.Option(False, "--plot", help="Plot data"),
    start: str = typer.Option(None, "--start", help="Start time (ISO format)"),
    end: str = typer.Option(None, "--end", help="End time (ISO format)"),
    columns: list[str] = typer.Option(None, "--columns", help="Columns to include"),
    position: str = typer.Option(None, "--position", help="Filter by position"),
    timeline_event_type: str = typer.Option(None, "--timeline-event-type", help="Filter by timeline event type"),
    list_filters: bool = typer.Option(False, "--list-filters", help="List all available positions and timeline event types"),
    timeline_events: bool = typer.Option(False, "--timeline-events", help="Show timeline event data"),
    markers: bool = typer.Option(False, "--markers", help="Show marker data"),
    marker_type: str = typer.Option(None, "--marker-type", help="Filter by marker type"),
):
    """Query and display data from the log visualizer API."""
    client = LogVisualizerClient()

    if list_filters:
        list_filters(client)
        return

    if timeline_events:
        display_timeline_event_tables(client, position=position, timeline_event_type=timeline_event_type)
        return

    if markers:
        display_markers_table(client, marker_type=marker_type, position=position, start=start, end=end)
        return

    if summary:
        # Get and display data summary
        try:
            response = requests.get(f"{client.base_url}/analysis/log_summary", timeout=10)
            response.raise_for_status()
            summary_data = response.json()

            console.print("\n[bold cyan]Data Summary[/bold cyan]")
            console.print(f"Total logs: {summary_data.get('total_logs', 0)}")
            console.print(
                f"Time range: {summary_data.get('time_range', {}).get('start', 'N/A')} to {summary_data.get('time_range', {}).get('end', 'N/A')}"
            )

            if "counts_by_position" in summary_data:
                console.print("\n[bold]Logs by Position:[/bold]")
                for pos, count in summary_data["counts_by_position"].items():
                    console.print(f"  {pos}: {count} logs")

        except Exception as e:
            console.print(f"[red]Error getting data summary: {e}[/red]")
        return

    if plot:
        # Parse time range
        start_time = None
        end_time = None
        if start:
            try:
                start_time = datetime.fromisoformat(start.replace("Z", "+00:00"))
            except ValueError:
                console.print(f"[red]Invalid start time format: {start}[/red]")
                return
        if end:
            try:
                end_time = datetime.fromisoformat(end.replace("Z", "+00:00"))
            except ValueError:
                console.print(f"[red]Invalid end time format: {end}[/red]")
                return

        # Get resource data and plot
        try:
            data = client.get_resource_data(start_time, end_time, columns, position=position)
            if data:
                # Create a simple plot using the first numeric column
                if columns and len(columns) > 0:
                    column_to_plot = columns[0]
                else:
                    # Find the first numeric column
                    numeric_columns = ["cpu_percent", "memory_percent", "disk_percent"]
                    column_to_plot = next((col for col in numeric_columns if any(col in str(d)) for d in data), "cpu_percent")

                console.print(f"\n[bold cyan]Plotting {column_to_plot} over time[/bold cyan]")
                # This would integrate with a plotting library like matplotlib
                console.print(f"Data points: {len(data)}")
                console.print(f"Time range: {data[0].get('timestamp', 'N/A')} to {data[-1].get('timestamp', 'N/A')}")
            else:
                console.print("[yellow]No data found for the specified time range[/yellow]")
        except Exception as e:
            console.print(f"[red]Error plotting data: {e}[/red]")
        return

    # Default behavior: show timeline events
    display_timeline_event_tables(client, position=position, timeline_event_type=timeline_event_type)


@app.command()
def columns(
    host: str = typer.Option("http://localhost", help="API host URL"),
    port: int = typer.Option(8000, help="API port"),
    position: str | None = typer.Option(None, "--position", help="Filter columns by position"),
):
    """List all available columns for anomaly detection."""
    client = LogVisualizerClient(host, port)

    # Define columns that should be excluded from anomaly detection
    # These are typically categorical or identifier columns that don't make sense for anomaly detection
    blacklisted_columns = [
        "file_name",
        "folder_name",
        "process_name",
        "library",
        "position",
    ]

    # Get timestamp range to get column availability
    start, end, column_availability = client.get_timestamp_range(range_type="common", position=position)

    if not column_availability:
        console.print("[yellow]No column availability information found.[/yellow]")
        return

    # Create a table to display columns
    table = Table(title="[cyan]Available Columns for Anomaly Detection[/cyan]", show_lines=True)
    table.add_column("[bold]Source[/bold]")
    table.add_column("[bold]Column Name[/bold]")
    table.add_column("[bold]Completeness[/bold]")
    table.add_column("[bold]First Seen[/bold]")
    table.add_column("[bold]Last Seen[/bold]")

    # Add columns from each source
    for source, columns in column_availability.items():
        for col, stats in columns.items():
            # Skip blacklisted columns
            if col.lower() in [b.lower() for b in blacklisted_columns]:
                continue

            if isinstance(stats, dict):
                completeness = stats.get("completeness", 0)
                first_seen = stats.get("first_seen", "Unknown")
                last_seen = stats.get("last_seen", "Unknown")

                # Format timestamps if they're strings
                if isinstance(first_seen, str) and first_seen != "Unknown":
                    try:
                        first_seen = parser.parse(first_seen).strftime("%Y-%m-%d %H:%M:%S")
                    except (ValueError, TypeError):
                        # Keep original value if parsing fails
                        pass

                if isinstance(last_seen, str) and last_seen != "Unknown":
                    try:
                        last_seen = parser.parse(last_seen).strftime("%Y-%m-%d %H:%M:%S")
                    except (ValueError, TypeError):
                        # Keep original value if parsing fails
                        pass

                table.add_row(
                    f"[green]{source.title()}[/green]",
                    f"[blue]{col}[/blue]",
                    f"[yellow]{completeness:.1f}%[/yellow]",
                    f"[dim]{first_seen}[/dim]",
                    f"[dim]{last_seen}[/dim]",
                )

    # Display the table
    if table.row_count > 0:
        console.print(table)
        console.print(f"\n[green]Total columns available: {table.row_count}[/green]")
    else:
        console.print("[yellow]No columns found.[/yellow]")


@app.command()
def anomalies(
    host: str = typer.Option("http://localhost", help="API host URL"),
    port: int = typer.Option(8000, help="API port"),
    start: str | None = typer.Option(None, "--start", help="Start time (ISO format)"),
    end: str | None = typer.Option(None, "--end", help="End time (ISO format)"),
    position: str | None = typer.Option(None, "--position", help="Filter by position"),
    method: str = typer.Option(
        "log_level",
        help="Anomaly detection method (log_level, zscore, iqr, control_chart, persist, level_shift)",
    ),
    column: str | None = typer.Option(
        None,
        "--column",
        help="Column to analyze for anomalies (if not provided, will analyze all columns with data)",
    ),
    error_threshold: float = typer.Option(0.05, help="Error rate threshold for log level method (0.0 to 1.0)"),
    warning_threshold: float = typer.Option(0.40, help="Warning rate threshold for log level method (0.0 to 1.0)"),
    threshold: float = typer.Option(3.0, help="Threshold for time series anomaly detection methods"),
    window_size: str = typer.Option("10min", help="Time window size for analysis"),
    list_columns: bool = typer.Option(False, "--list-columns", help="List available columns for anomaly detection"),
    min_completeness: float = typer.Option(
        0.0,
        "--min-completeness",
        help="Minimum data completeness percentage for columns to analyze (0-100)",
    ),
):
    """List detected anomalies with their time ranges and descriptions."""
    client = LogVisualizerClient(host, port)

    # Define columns that should be excluded from anomaly detection
    # These are typically categorical or identifier columns that don't make sense for anomaly detection
    blacklisted_columns = [
        "file_name",
        "folder_name",
        "process_name",
        "library",
        "position",
    ]

    # If list_columns is True, call the columns command and exit
    if list_columns:
        columns(host=host, port=port, position=position)
        return

    # Parse start and end times if provided
    start_time = parser.parse(start) if start else None
    end_time = parser.parse(end) if end else None

    try:
        if method == "log_level":
            # Create a table to display log level anomalies
            table = Table(title="[cyan]Detected Log Level Anomalies[/cyan]", show_lines=True)
            table.add_column("[bold]Position[/bold]")
            table.add_column("[bold]Start Time[/bold]")
            table.add_column("[bold]End Time[/bold]")
            table.add_column("[bold]Type[/bold]")
            table.add_column("[bold]Description[/bold]")
            table.add_column("[bold]Details[/bold]")

            # Get all positions if no position is specified
            positions_to_check = [position] if position else []
            if not positions_to_check:
                # Get log summary to get all positions
                try:
                    response = requests.get(f"{client.base_url}/analysis/log_summary", timeout=10)
                    response.raise_for_status()
                    log_summary = response.json()
                    if log_summary and "counts_by_position" in log_summary:
                        positions_to_check = list(log_summary["counts_by_position"].keys())
                except requests.RequestException as e:
                    console.print(f"[red]Error getting log summary: {e}[/red]")
                    positions_to_check = []

            # Track total anomalies found
            total_anomalies = 0

            # Check each position
            for pos in positions_to_check:
                try:
                    # Get log level anomalies for this position
                    log_anomalies = client.detect_log_level_anomalies(
                        start=start_time,
                        end=end_time,
                        position=pos,
                        error_threshold=error_threshold,
                        warning_threshold=warning_threshold,
                        window_size=window_size,
                    )

                    # Add log level anomalies to table
                    for anomaly in log_anomalies:
                        start = parser.parse(anomaly["start"]) if isinstance(anomaly["start"], str) else anomaly["start"]
                        end = parser.parse(anomaly["end"]) if isinstance(anomaly["end"], str) else anomaly["end"]

                        description = f"High {anomaly['type']} rate detected"
                        details = f"Rate: {anomaly['rate']:.2%}, Threshold: {anomaly['threshold']:.2%}"

                        table.add_row(
                            f"[green]{pos}[/green]",
                            f"[yellow]{start.strftime('%Y-%m-%d %H:%M:%S')}[/yellow]",
                            f"[yellow]{end.strftime('%Y-%m-%d %H:%M:%S')}[/yellow]",
                            f"[red]{anomaly['type'].upper()}[/red]",
                            description,
                            details,
                        )

                        total_anomalies += 1
                except Exception as e:
                    console.print(f"[yellow]Warning: Error processing position {pos}: {e!s}[/yellow]")

            # Display the table
            if table.row_count > 0:
                console.print(table)
                console.print(
                    f"\n[green]Total log level anomalies detected: {total_anomalies} across {len(positions_to_check)} positions[/green]"
                )
            else:
                console.print("[yellow]No log level anomalies detected in the specified time range.[/yellow]")
        else:
            # Get available columns and their sources
            _, _, column_availability = client.get_timestamp_range(range_type="common")
            if not column_availability:
                console.print("[yellow]No column availability information found.[/yellow]")
                return

            # Identify resource columns
            resource_columns = set()
            if "resource" in column_availability:
                resource_columns = set(column_availability["resource"].keys())

            # If a specific column is provided, check if it's a resource column
            if column:
                is_resource_column = column in resource_columns
                # For resource columns, we only need to process once, not per position
                if is_resource_column:
                    positions_to_check = [None]  # Only process once with no position
                else:
                    # Get all positions if no position is specified
                    positions_to_check = [position] if position else []
                    if not positions_to_check:
                        # Get log summary to get all positions
                        try:
                            response = requests.get(f"{client.base_url}/analysis/log_summary", timeout=10)
                            response.raise_for_status()
                            log_summary = response.json()
                            if log_summary and "counts_by_position" in log_summary:
                                positions_to_check = list(log_summary["counts_by_position"].keys())
                        except requests.RequestException as e:
                            console.print(f"[red]Error getting log summary: {e}[/red]")
                            positions_to_check = []
            else:
                # If no column is specified, we need to process both resource and non-resource columns
                # Start with resource columns (position=None)
                positions_to_check = [None]
                # Then add positions for non-resource columns
                try:
                    response = requests.get(f"{client.base_url}/analysis/log_summary", timeout=10)
                    response.raise_for_status()
                    log_summary = response.json()
                    if log_summary and "counts_by_position" in log_summary:
                        positions_to_check.extend(list(log_summary["counts_by_position"].keys()))
                except requests.RequestException as e:
                    console.print(f"[red]Error getting log summary: {e}[/red]")

            # Create a table to display time series anomalies
            table = Table(
                title=f"[cyan]Detected Time Series Anomalies (Method: {method})[/cyan]",
                show_lines=True,
            )
            table.add_column("[bold]Position[/bold]")
            table.add_column("[bold]Column[/bold]")
            table.add_column("[bold]Source[/bold]")
            table.add_column("[bold]Start Time[/bold]")
            table.add_column("[bold]End Time[/bold]")
            table.add_column("[bold]Type[/bold]")
            table.add_column("[bold]Value[/bold]")
            table.add_column("[bold]Description[/bold]")
            table.add_column("[bold]Details[/bold]")

            # Track total anomalies found
            total_anomalies = 0
            total_columns_analyzed = 0
            error_columns = []

            # Check each position (or once for resource columns)
            for pos in positions_to_check:
                try:
                    # Get available columns for this position
                    _, _, position_column_availability = client.get_timestamp_range(range_type="common", position=pos)

                    if not position_column_availability:
                        console.print(f"[yellow]No column availability information found for position {pos}.[/yellow]")
                        continue

                    # Collect columns to analyze
                    columns_to_analyze = []

                    if column:
                        # If a specific column is provided, check if it exists
                        column_exists = False
                        for source, columns in position_column_availability.items():
                            if column in columns:
                                column_exists = True
                                columns_to_analyze.append((source, column, columns[column]))
                                break

                        if not column_exists:
                            console.print(f"[yellow]Column '{column}' does not exist for position {pos}. Skipping.[/yellow]")
                            continue
                    else:
                        # If no column is provided, analyze all columns with completeness > min_completeness
                        # but exclude blacklisted columns
                        for source, columns in position_column_availability.items():
                            for col, stats in columns.items():
                                # Skip blacklisted columns
                                if col.lower() in [b.lower() for b in blacklisted_columns]:
                                    continue

                                # For resource columns, only process them when pos is None
                                if col in resource_columns:
                                    if pos is not None:
                                        continue
                                elif pos is None:
                                    # Skip non-resource columns when pos is None
                                    continue

                                if isinstance(stats, dict):
                                    completeness = stats.get("completeness", 0)
                                    if completeness > min_completeness:
                                        columns_to_analyze.append((source, col, stats))

                    if not columns_to_analyze:
                        if pos is None:
                            console.print(f"[yellow]No resource columns found with completeness > {min_completeness}%.[/yellow]")
                        else:
                            console.print(f"[yellow]No columns found with completeness > {min_completeness}% for position {pos}.[/yellow]")
                        continue

                    # Analyze each column for this position
                    with Progress() as progress:
                        task = progress.add_task(
                            f"[cyan]Analyzing {len(columns_to_analyze)} columns for position {pos if pos else 'resource metrics'}...",
                            total=len(columns_to_analyze),
                        )

                        for source, col, stats in columns_to_analyze:
                            try:
                                # Get time series anomalies for this column
                                time_series_anomalies = client.detect_time_series_anomalies(
                                    column=col,
                                    method=method,
                                    start=start_time,
                                    end=end_time,
                                    position=pos,  # Will be None for resource columns
                                    window_size=window_size,
                                    zscore_threshold=threshold if method == "zscore" else None,
                                    iqr_factor=threshold if method == "iqr" else None,
                                    control_chart_std=(threshold if method == "control_chart" else None),
                                )

                                # Add time series anomalies to table
                                for anomaly in time_series_anomalies:
                                    start = parser.parse(anomaly["start"]) if isinstance(anomaly["start"], str) else anomaly["start"]
                                    end = parser.parse(anomaly["end"]) if isinstance(anomaly["end"], str) else anomaly["end"]

                                    # Create description based on anomaly type
                                    if anomaly["type"] == "zscore":
                                        description = "Unusual value detected"
                                        details = f"Z-score: {anomaly['details']['z_score']:.2f}, Value: {anomaly['value']:.2f}"
                                    elif anomaly["type"] == "iqr":
                                        description = "Outlier detected"
                                        details = f"Value: {anomaly['value']:.2f}, Q1: {anomaly['details']['q1']:.2f}, Q3: {anomaly['details']['q3']:.2f}"
                                    elif anomaly["type"] == "control_chart":
                                        description = "Control chart violation"
                                        details = f"Value: {anomaly['value']:.2f}, Mean: {anomaly['details']['mean']:.2f}, Std: {anomaly['details']['std']:.2f}"
                                    elif anomaly["type"] == "persist":
                                        description = "Persistence anomaly"
                                        details = f"Value: {anomaly['value']:.2f}"
                                    elif anomaly["type"] == "level_shift":
                                        description = "Level shift detected"
                                        details = f"Shift magnitude: {anomaly['details']['shift_magnitude']:.2f}"
                                    else:
                                        description = "Anomaly detected"
                                        details = str(anomaly["details"])

                                    # Add category frequencies to details if available
                                    if "category_frequencies" in anomaly["details"]:
                                        cat_freqs = anomaly["details"]["category_frequencies"]
                                        if cat_freqs:
                                            # Format category frequencies as a string
                                            cat_freq_str = ", ".join([f"{cat}: {freq:.2%}" for cat, freq in cat_freqs.items()])
                                            details += f"\nCategory frequencies: {cat_freq_str}"

                                    table.add_row(
                                        f"[green]{pos if pos else 'system'}[/green]",
                                        f"[blue]{col}[/blue]",
                                        f"[green]{source.title()}[/green]",
                                        f"[yellow]{start.strftime('%Y-%m-%d %H:%M:%S')}[/yellow]",
                                        f"[yellow]{end.strftime('%Y-%m-%d %H:%M:%S')}[/yellow]",
                                        f"[red]{anomaly['type'].upper()}[/red]",
                                        f"[cyan]{anomaly['value']:.2f}[/cyan]",
                                        description,
                                        details,
                                    )

                                    total_anomalies += 1
                            except Exception as e:
                                # Log the error but continue processing other columns
                                error_msg = str(e)
                                if "attempt to get argmax of an empty sequence" in error_msg:
                                    console.print(f"[yellow]Warning: No data available for column '{col}' in position '{pos}'.[/yellow]")
                                else:
                                    console.print(
                                        f"[yellow]Warning: Error processing column '{col}' in position '{pos}': {error_msg}[/yellow]"
                                    )
                                error_columns.append((pos, col))

                            # Update progress
                            progress.update(task, advance=1)
                            total_columns_analyzed += 1
                except Exception as e:
                    console.print(f"[yellow]Warning: Error processing position {pos}: {e!s}[/yellow]")

            # Display the table
            if table.row_count > 0:
                console.print(table)
                console.print(
                    f"\n[green]Total time series anomalies detected: {total_anomalies} across {total_columns_analyzed} columns in {len(positions_to_check)} positions[/green]"
                )

                if error_columns:
                    console.print(
                        f"\n[yellow]Note: {len(error_columns)} columns could not be analyzed due to errors or insufficient data.[/yellow]"
                    )
                    if len(error_columns) <= 10:  # Only show details if there are 10 or fewer errors
                        console.print("[yellow]Columns with errors:[/yellow]")
                        for pos, col in error_columns:
                            console.print(f"  - {pos}: {col}")
                    else:
                        console.print("[yellow]Too many errors to list. First 5:[/yellow]")
                        for pos, col in error_columns[:5]:
                            console.print(f"  - {pos}: {col}")
                        console.print(f"[yellow]... and {len(error_columns) - 5} more.[/yellow]")
            else:
                console.print(
                    f"[yellow]No time series anomalies detected in the specified time range across {total_columns_analyzed} columns in {len(positions_to_check)} positions.[/yellow]"
                )

                if error_columns:
                    console.print(
                        f"\n[yellow]Note: {len(error_columns)} columns could not be analyzed due to errors or insufficient data.[/yellow]"
                    )
                    if len(error_columns) <= 10:  # Only show details if there are 10 or fewer errors
                        console.print("[yellow]Columns with errors:[/yellow]")
                        for pos, col in error_columns:
                            console.print(f"  - {pos}: {col}")
                    else:
                        console.print("[yellow]Too many errors to list. First 5:[/yellow]")
                        for pos, col in error_columns[:5]:
                            console.print(f"  - {pos}: {col}")
                        console.print(f"[yellow]... and {len(error_columns) - 5} more.[/yellow]")

    except Exception as e:
        console.print(f"[red]Error detecting anomalies: {e!s}[/red]")
        raise typer.Exit(code=1)


@app.command()
def logs(
    host: str = typer.Option("http://localhost", help="API host URL"),
    port: int = typer.Option(8000, help="API port"),
    start: str | None = typer.Option(None, "--start", help="Start time (ISO format)"),
    end: str | None = typer.Option(None, "--end", help="End time (ISO format)"),
    position: str | None = typer.Option(None, "--position", help="Filter by position"),
    limit: int = typer.Option(100, "--limit", help="Maximum number of logs to display"),
    json_format: bool = typer.Option(False, "--json", help="Output logs as JSON strings (one per line)"),
    table_format: bool = typer.Option(True, "--table", help="Output logs in a table format"),
):
    """Display raw structured logs from the Log Visualizer API."""
    client = LogVisualizerClient(host, port)

    # Parse start and end times if provided
    start_time = parser.parse(start) if start else None
    end_time = parser.parse(end) if end else None

    # If no time range is specified, get the common range
    if not start_time or not end_time:
        common_start, common_end, _ = client.get_timestamp_range(range_type="common", position=position)
        if not start_time:
            start_time = common_start
        if not end_time:
            end_time = common_end

        if not start_time or not end_time:
            console.print("[red]Error: Unable to determine the timestamp range. Please check the data source.[/red]")
            raise typer.Exit(code=1)

    console.print(f"[green]Fetching logs from {start_time} to {end_time}[/green]")
    if position:
        console.print(f"[green]Filtering by position: {position}[/green]")

    # Fetch logs with pagination
    all_logs = []
    offset = 0
    batch_size = min(1000, limit)  # Use a reasonable batch size

    with Progress() as progress:
        task = progress.add_task("[cyan]Fetching logs...", total=limit)

        while len(all_logs) < limit:
            batch = client.get_logs(start=start_time, end=end_time, position=position, limit=batch_size, offset=offset)

            if not batch:
                break

            all_logs.extend(batch)
            offset += len(batch)
            progress.update(task, completed=min(len(all_logs), limit))

            if len(batch) < batch_size:
                break

    if not all_logs:
        console.print("[yellow]No logs found for the specified criteria.[/yellow]")
        return

    # Limit to the requested number of logs
    all_logs = all_logs[:limit]

    if json_format:
        # Output logs as JSON strings (one per line)
        for log in all_logs:
            console.print(json.dumps(log))
    elif table_format:
        # Create a table to display logs
        table = Table(title="[cyan]Structured Logs[/cyan]", show_lines=True)

        # Determine columns based on the first log entry
        if all_logs:
            # Common columns that are likely to be present
            common_columns = ["timestamp", "level", "position", "message"]

            # Add common columns first
            for col in common_columns:
                if col in all_logs[0]:
                    table.add_column(f"[bold]{col}[/bold]")

            # Add other columns that might be present
            for key in all_logs[0].keys():
                if key not in common_columns:
                    table.add_column(f"[bold]{key}[/bold]")

            # Add rows for each log
            for log in all_logs:
                row_data = []

                # Add common columns first
                for col in common_columns:
                    if col in log:
                        value = log[col]
                        # Format timestamp if present
                        if col == "timestamp" and isinstance(value, str):
                            try:
                                dt = parser.parse(value)
                                value = dt.strftime("%Y-%m-%d %H:%M:%S")
                            except (ValueError, TypeError):
                                # Keep original value if parsing fails
                                pass
                        # Color code log levels
                        elif col == "level":
                            if value.upper() == "ERROR":
                                value = f"[red]{value}[/red]"
                            elif value.upper() == "WARNING":
                                value = f"[yellow]{value}[/yellow]"
                            elif value.upper() == "INFO":
                                value = f"[green]{value}[/green]"
                        row_data.append(str(value))

                # Add other columns
                for key in all_logs[0].keys():
                    if key not in common_columns:
                        row_data.append(str(log.get(key, "")))

                table.add_row(*row_data)

        # Display the table
        console.print(table)
        console.print(f"\n[green]Total logs displayed: {len(all_logs)}[/green]")
    else:
        # Fallback to simple display
        for log in all_logs:
            console.print(log)
            console.print("---")

        console.print(f"\n[green]Total logs displayed: {len(all_logs)}[/green]")


@app.command()
def timeline_events(
    start: str = typer.Option(None, "--start", help="Start timestamp (ISO format)"),
    end: str = typer.Option(None, "--end", help="End timestamp (ISO format)"),
    position: str = typer.Option(None, "--position", help="Filter by position"),
    timeline_event_type: str = typer.Option(None, "--timeline-event-type", help="Filter by timeline event type"),
    list_filters: bool = typer.Option(False, "--list-filters", help="List all available positions and timeline event types"),
    timeline_events: bool = typer.Option(False, "--timeline-events", help="Show timeline event data"),
    markers: bool = typer.Option(False, "--markers", help="Show marker data"),
    marker_type: str = typer.Option(None, "--marker-type", help="Filter by marker type"),
    visual: bool = typer.Option(False, "--visual", help="Display timeline events as a visual timeline"),
):
    """Query timeline events from the log visualizer API."""
    client = LogVisualizerClient()

    if list_filters:
        list_filters(client)
        return

    if visual:
        display_timeline_visualization(client, position=position, timeline_event_type=timeline_event_type)
        return
    elif timeline_events:
        display_timeline_event_tables(client, position=position, timeline_event_type=timeline_event_type)
        return
    elif markers:
        display_markers_table(client, marker_type=marker_type, position=position, start=start, end=end)
        return

    # Default behavior: show timeline events
    display_timeline_event_tables(client, position=position, timeline_event_type=timeline_event_type)


def display_timeline_event_tables(client: LogVisualizerClient, position: str | None = None, timeline_event_type: str | None = None) -> None:
    """Display timeline events in a formatted table."""
    try:
        timeline_events = client.get_timeline_events(position_id=position, timeline_event_type=timeline_event_type)

        if not timeline_events:
            console.print("[yellow]No timeline events found for the specified criteria.[/yellow]")
            return

        # Group timeline events by position and type
        grouped_timeline_events = {}
        for timeline_event in timeline_events:
            pos_id = timeline_event.get("position_id", "unknown")
            timeline_event_name = timeline_event.get("name", "unknown")

            if pos_id not in grouped_timeline_events:
                grouped_timeline_events[pos_id] = {}
            if timeline_event_name not in grouped_timeline_events[pos_id]:
                grouped_timeline_events[pos_id][timeline_event_name] = []

            grouped_timeline_events[pos_id][timeline_event_name].append(timeline_event)

        # Display timeline events grouped by position and type
        for pos_id, timeline_event_types in grouped_timeline_events.items():
            console.print(f"\n[bold blue]Position: {pos_id}[/bold blue]")

            for timeline_event_name, timeline_events_list in timeline_event_types.items():
                console.print(f"\n[bold green]Timeline Event Type: {timeline_event_name}[/bold green]")

                # Create table
                table = Table(title=f"Timeline Events - {timeline_event_name}")
                table.add_column("ID", style="cyan")
                table.add_column("Start Time", style="green")
                table.add_column("End Time", style="red")
                table.add_column("Duration", style="yellow")
                table.add_column("Markers", style="bright_magenta")
                table.add_column("Metadata", style="magenta")

                for timeline_event in timeline_events_list:
                    start_time = timeline_event.get("start_time", "unknown")
                    end_time = timeline_event.get("end_time", "unknown")
                    duration = timeline_event.get("duration", 0)
                    metadata = timeline_event.get("metadata", {})
                    markers = timeline_event.get("markers", [])

                    # Format duration
                    duration_str = format_duration(duration)

                    # Format metadata
                    metadata_str = ", ".join([f"{k}={v}" for k, v in metadata.items()]) if metadata else "None"

                    # Format markers
                    if markers:
                        marker_counts = {}
                        for marker in markers:
                            marker_name = marker.get("name", "unknown")
                            marker_counts[marker_name] = marker_counts.get(marker_name, 0) + 1
                        markers_str = ", ".join([f"{name}({count})" for name, count in marker_counts.items()])
                    else:
                        markers_str = "None"

                    table.add_row(
                        timeline_event.get("id", "unknown"),
                        start_time,
                        end_time,
                        duration_str,
                        markers_str,
                        metadata_str,
                    )

                console.print(table)

    except Exception as e:
        console.print(f"[red]Error displaying timeline events: {e}[/red]")


def list_filters(client: LogVisualizerClient) -> None:
    """List all available positions and timeline event types."""
    try:
        # Get positions
        positions = client.get_positions()

        # Get timeline event types
        timeline_event_types = client.get_timeline_event_types()

        console.print("\n[bold blue]Available Filters:[/bold blue]")

        # Display positions
        if positions:
            console.print("\n[bold green]Positions:[/bold green]")
            for position in sorted(positions):
                console.print(f"  [green]{position}[/green]")
        else:
            console.print("\n[yellow]No positions available[/yellow]")

        # Display timeline event types
        if timeline_event_types:
            console.print("\n[bold green]Timeline Event Types:[/bold green]")
            for timeline_event_type in sorted(timeline_event_types):
                console.print(f"  [green]{timeline_event_type}[/green]")
        else:
            console.print("\n[yellow]No timeline event types available[/yellow]")

        # Get marker types
        marker_types = client.get_marker_types()

        # Display marker types
        if marker_types:
            console.print("\n[bold magenta]Marker Types:[/bold magenta]")
            for marker_type in sorted(marker_types):
                console.print(f"  [magenta]{marker_type}[/magenta]")
        else:
            console.print("\n[yellow]No marker types available[/yellow]")

    except Exception as e:
        console.print(f"[red]Error listing filters: {e}[/red]")


def display_timeline_visualization(
    client: LogVisualizerClient, position: str | None = None, timeline_event_type: str | None = None
) -> None:
    """Display timeline events as a visual timeline in the console."""
    try:
        # Get timeline events with children included
        timeline_events = client.get_timeline_events(position_id=position, timeline_event_type=timeline_event_type)

        if not timeline_events:
            console.print("[yellow]No timeline events found for the specified criteria.[/yellow]")
            return

        # Group events by position
        events_by_position = {}
        for event in timeline_events:
            pos_id = event.get("position_id", "unknown")
            if pos_id not in events_by_position:
                events_by_position[pos_id] = []
            events_by_position[pos_id].append(event)

        # For each position, create a visual timeline
        for position_id, events in events_by_position.items():
            console.print(f"\n[bold cyan]Timeline for Position: {position_id}[/bold cyan]")

            # Sort events by start time
            events.sort(key=lambda x: parser.parse(x["start_time"]))

            # Find the overall time range
        start_times = [parser.parse(event["start_time"]) for event in events]
        end_times = [parser.parse(event["end_time"]) for event in events if event["end_time"]]

        if not start_times:
            console.print("[yellow]No events found for visualization.[/yellow]")
            return

        overall_start = min(start_times)

        if not end_times:
            console.print("[yellow]No complete events found for visualization.[/yellow]")
            return

        overall_end = max(end_times)
        total_duration = (overall_end - overall_start).total_seconds()

        console.print(
            f"[dim]Time range: {overall_start.strftime('%Y-%m-%d %H:%M:%S')} to {overall_end.strftime('%Y-%m-%d %H:%M:%S')} ({total_duration:.1f}s)[/dim]"
        )

        # Create visual timeline
        display_visual_timeline(events, overall_start, overall_end, position_id)

    except Exception as e:
        console.print(f"[red]Error displaying timeline visualization: {e}[/red]")


def display_visual_timeline(
    events: list[dict], overall_start: datetime, overall_end: datetime, position_id: str, max_events: int = 20
) -> None:
    """Display a visual timeline of events in the console, with children on lines below their parent."""
    # Get console width for scaling
    console_width = console.size[0] - 40  # Leave more margin for labels
    timeline_width = max(50, console_width)

    # Sort events by start time
    events.sort(key=lambda x: parser.parse(x["start_time"]))

    # Only consider events with valid end_time for parent/child logic
    valid_events = [e for e in events if e.get("end_time")]

    # Limit the number of parent events for readability
    parent_events = [
        e
        for e in valid_events
        if not any(
            parser.parse(e["start_time"]) >= parser.parse(o["start_time"])
            and parser.parse(e["end_time"]) <= parser.parse(o["end_time"])
            and e["id"] != o["id"]
            for o in valid_events
        )
    ]
    if len(parent_events) > max_events:
        console.print(f"[yellow]Note: Showing first {max_events} parent events out of {len(parent_events)} for readability.[/yellow]")
        parent_events = parent_events[:max_events]

    # Helper to find children of a parent event
    def get_children(parent, all_events):
        p_start = parser.parse(parent["start_time"])
        p_end = parser.parse(parent["end_time"])
        children = []
        for e in all_events:
            if e["id"] == parent["id"] or not e.get("end_time"):
                continue
            e_start = parser.parse(e["start_time"])
            e_end = parser.parse(e["end_time"])
            if (
                e_start >= p_start
                and e_end <= p_end
                and not any(
                    # e is a child of another event that's also a child of parent
                    o["id"] != parent["id"]
                    and o["id"] != e["id"]
                    and o.get("end_time")
                    and parser.parse(o["start_time"]) <= e_start
                    and parser.parse(o["end_time"]) >= e_end
                    and parser.parse(o["start_time"]) >= p_start
                    and parser.parse(o["end_time"]) <= p_end
                    for o in all_events
                )
            ):
                children.append(e)
        return children

    # Helper to stack children if they overlap
    def stack_children(children):
        rows = []
        for child in sorted(children, key=lambda c: parser.parse(c["start_time"])):
            if not child.get("end_time"):
                continue
            placed = False
            for row in rows:
                if all(
                    parser.parse(child["end_time"]) <= parser.parse(other["start_time"])
                    or parser.parse(child["start_time"]) >= parser.parse(other["end_time"])
                    for other in row
                    if other.get("end_time")
                ):
                    row.append(child)
                    placed = True
                    break
            if not placed:
                rows.append([child])
        return rows

    # Color map
    color_map = {"protocol": "blue", "data_acquisition": "green", "analysis": "yellow", "error": "red"}

    # For each parent event, print its timeline and its children below
    for parent in parent_events:
        p_start = parser.parse(parent["start_time"])
        p_end = parser.parse(parent["end_time"])
        duration = (p_end - p_start).total_seconds()
        if duration <= 0:
            continue
        # Print parent label
        label = f"{parent['name']} ({duration:.1f}s)"
        color = color_map.get(parent["name"].lower(), "white")
        console.print(
            f"\n[bold]{label}[/bold]  [dim]{p_start.strftime('%Y-%m-%d %H:%M:%S')} to {p_end.strftime('%Y-%m-%d %H:%M:%S')}[/dim]"
        )
        # Draw parent timeline
        parent_line = [" "] * timeline_width
        for i in range(timeline_width):
            parent_line[i] = "█"
        console.print(f"[{color}]{''.join(parent_line)}[/{color}]")
        # Find children
        children = get_children(parent, valid_events)
        if children:
            # Stack children if they overlap
            child_rows = stack_children(children)
            for row in child_rows:
                child_line = [" "] * timeline_width
                for child in row:
                    c_start = parser.parse(child["start_time"])
                    c_end = parser.parse(child["end_time"])
                    c_color = color_map.get(child["name"].lower(), "green")
                    # Position relative to parent
                    start_pos = int(((c_start - p_start).total_seconds() / duration) * timeline_width)
                    end_pos = int(((c_end - p_start).total_seconds() / duration) * timeline_width)
                    start_pos = max(0, min(start_pos, timeline_width - 1))
                    end_pos = max(start_pos + 1, min(end_pos, timeline_width))
                    for i in range(start_pos, end_pos):
                        child_line[i] = "▄"
                    # Add label if space
                    label = child["name"]
                    label_start = end_pos + 1
                    available = timeline_width - label_start - 1
                    if available > 0:
                        label = label[:available]
                        for i, char in enumerate(label):
                            if label_start + i < timeline_width:
                                child_line[label_start + i] = char
                # Print child line
                console.print(f"[green]{''.join(child_line)}[/green]")
        else:
            console.print(f"[dim](no children)[/dim]")

    # Add legend
    console.print(f"\n[bold]Legend:[/bold]")
    console.print(f"[blue]█[/blue] Parent event timeline")
    console.print(f"[green]▄[/green] Child event(s) (stacked if overlap)")
    console.print(f"[dim](no children)[/dim] Parent has no children")


@app.command()
def timeline_visual(
    host: str = typer.Option("http://localhost", help="API host URL"),
    port: int = typer.Option(8000, help="API port"),
    start: str | None = typer.Option(None, "--start", help="Start timestamp (ISO format)"),
    end: str | None = typer.Option(None, "--end", help="End timestamp (ISO format)"),
    position: str | None = typer.Option(None, "--position", help="Filter by position"),
    timeline_event_type: str | None = typer.Option(None, "--timeline-event-type", help="Filter by timeline event type"),
):
    """Display timeline events as a visual timeline in the console."""
    client = LogVisualizerClient(host, port)
    display_timeline_visualization(client, position=position, timeline_event_type=timeline_event_type)


@app.command()
def markers(
    host: str = typer.Option("http://localhost", help="API host URL"),
    port: int = typer.Option(8000, help="API port"),
    marker_type: str | None = typer.Option(None, "--marker-type", help="Filter by marker type"),
    timeline_event_id: str | None = typer.Option(None, "--timeline-event-id", help="Filter by timeline event ID"),
    position: str | None = typer.Option(None, "--position", help="Filter by position"),
    start: str | None = typer.Option(None, "--start", help="Start timestamp (ISO format)"),
    end: str | None = typer.Option(None, "--end", help="End timestamp (ISO format)"),
    list_filters: bool = typer.Option(False, "--list-filters", help="List all available marker types"),
):
    """Query timeline markers from the log visualizer API."""
    client = LogVisualizerClient(host, port)

    if list_filters:
        list_filters_command(client)
        return

    display_markers_table(client, marker_type=marker_type, timeline_event_id=timeline_event_id, position=position, start=start, end=end)


def display_markers_table(
    client: LogVisualizerClient,
    marker_type: str | None = None,
    timeline_event_id: str | None = None,
    position: str | None = None,
    start: str | None = None,
    end: str | None = None,
) -> None:
    """Display markers in a formatted table."""
    try:
        markers = client.get_timeline_markers(
            marker_type=marker_type,
            timeline_event_id=timeline_event_id,
            position_id=position,
            start=start,
            end=end,
        )

        if not markers:
            console.print("[yellow]No markers found for the specified criteria.[/yellow]")
            return

        # Group markers by position and type
        grouped_markers = {}
        for marker in markers:
            pos_id = marker.get("position_id", "unknown")
            marker_name = marker.get("name", "unknown")

            if pos_id not in grouped_markers:
                grouped_markers[pos_id] = {}
            if marker_name not in grouped_markers[pos_id]:
                grouped_markers[pos_id][marker_name] = []

            grouped_markers[pos_id][marker_name].append(marker)

        # Display markers grouped by position and type
        for pos_id, marker_types in grouped_markers.items():
            console.print(f"\n[bold blue]Position: {pos_id}[/bold blue]")

            for marker_name, markers_list in marker_types.items():
                console.print(f"\n[bold magenta]Marker Type: {marker_name}[/bold magenta]")

                # Create table
                table = Table(title=f"Timeline Markers - {marker_name}")
                table.add_column("ID", style="cyan")
                table.add_column("Timestamp", style="green")
                table.add_column("Log Event", style="yellow")
                table.add_column("Metadata", style="magenta")

                for marker in markers_list:
                    timestamp = marker.get("timestamp", "unknown")
                    log_event = marker.get("log_event", "unknown")
                    metadata = marker.get("metadata", {})

                    # Format metadata
                    metadata_str = ", ".join([f"{k}={v}" for k, v in metadata.items()]) if metadata else "None"

                    table.add_row(
                        marker.get("id", "unknown"),
                        timestamp,
                        log_event,
                        metadata_str,
                    )

                console.print(table)

    except Exception as e:
        console.print(f"[red]Error displaying markers: {e}[/red]")


def list_filters_command(client: LogVisualizerClient) -> None:
    """List all available positions, timeline event types, and marker types."""
    list_filters(client)  # This function already handles all filter types including markers


@app.command()
def investigate(
    target_time: str = typer.Argument(..., help="Target time for investigation (ISO format)"),
    host: str = typer.Option("http://localhost", help="API host URL"),
    port: int = typer.Option(8000, help="API port"),
    window_minutes: int = typer.Option(60, help="Time window around target (total window, will be split ±)"),
    position: str | None = typer.Option(None, "--position", help="Optional position filter for spatial context"),
    include_bursts: bool = typer.Option(True, "--include-bursts/--no-bursts", help="Include event burst detection"),
    burst_window: int = typer.Option(5, help="Burst detection window in minutes"),
    burst_threshold: int = typer.Option(3, help="Burst detection threshold"),
):
    """Generate a comprehensive investigation report around a specific time.
    
    This command provides all relevant context for incident investigation including:
    - Critical events (ERROR/WARNING) within the investigation window
    - Active timeline events spanning the target time
    - Resource snapshot at the target time
    - Concurrent activity across positions
    - Recent anomalies in expanded window
    - Optional event burst detection
    """
    client = LogVisualizerClient(host, port)
    
    console.print(f"[bold blue]🔍 Investigation Report for {target_time}[/bold blue]")
    console.print(f"[dim]Window: ±{window_minutes//2} minutes | Position: {position or 'All'}[/dim]\n")
    
    try:
        # Parse target time for validation
        target_dt = parser.parse(target_time)
        
        # Get investigation context
        with console.status("[bold green]Gathering investigation context..."):
            context = client.get_investigation_context(
                target_time=target_time,
                window_minutes=window_minutes,
                position=position
            )
        
        if not context:
            console.print("[red]Failed to get investigation context[/red]")
            return
            
        # Display investigation summary
        _display_investigation_summary(context)
        
        # Display critical events
        _display_critical_events(context.get("critical_events", []))
        
        # Display active timelines
        _display_active_timelines(context.get("active_timelines", []))
        
        # Display resource snapshot
        _display_resource_snapshot(context.get("resource_snapshot", {}))
        
        # Display concurrent activity
        _display_concurrent_activity(context.get("concurrent_activity", []))
        
        # Display recent anomalies
        _display_recent_anomalies(context.get("recent_anomalies", []))
        
        # Optional event burst detection
        if include_bursts:
            console.print(f"\n[bold yellow]📈 Event Burst Analysis[/bold yellow]")
            
            # Calculate burst detection window around target time
            burst_start = (target_dt - timedelta(hours=2)).isoformat()
            burst_end = (target_dt + timedelta(hours=2)).isoformat()
            
            with console.status("[bold green]Detecting event bursts..."):
                bursts = client.detect_event_bursts(
                    window_minutes=burst_window,
                    threshold_count=burst_threshold,
                    start_time=burst_start,
                    end_time=burst_end,
                    position=position
                )
            
            _display_event_bursts(bursts)
        
        console.print(f"\n[bold green]✅ Investigation report complete[/bold green]")
        
    except Exception as e:
        console.print(f"[red]Error during investigation: {e}[/red]")


def _display_investigation_summary(context: dict) -> None:
    """Display investigation summary."""
    console.print(f"[bold cyan]📊 Investigation Summary[/bold cyan]")
    
    target_time = context.get("target_time", "Unknown")
    window = context.get("window", {})
    position = context.get("position")
    
    console.print(f"  • Target Time: {target_time}")
    console.print(f"  • Window: {window.get('start', 'Unknown')} to {window.get('end', 'Unknown')}")
    console.print(f"  • Position Filter: {position or 'None'}")
    console.print(f"  • Critical Events: {len(context.get('critical_events', []))}")
    console.print(f"  • Active Timelines: {len(context.get('active_timelines', []))}")
    console.print(f"  • Concurrent Positions: {len(context.get('concurrent_activity', []))}")
    console.print(f"  • Recent Anomalies: {len(context.get('recent_anomalies', []))}")


def _display_critical_events(events: list) -> None:
    """Display critical events table."""
    if not events:
        console.print(f"\n[yellow]⚠️  No critical events found[/yellow]")
        return
    
    console.print(f"\n[bold red]🚨 Critical Events ({len(events)})[/bold red]")
    
    table = Table()
    table.add_column("Timestamp", style="green")
    table.add_column("Level", style="red")
    table.add_column("Event", style="yellow")
    table.add_column("Message", style="white")
    table.add_column("Process", style="cyan")
    table.add_column("Score", style="magenta")
    
    for event in events[:20]:  # Limit to top 20 events
        table.add_row(
            event.get("timestamp", "")[:19],  # Trim milliseconds
            event.get("log_level", ""),
            event.get("log_event", ""),
            (event.get("message", "")[:50] + "...") if len(event.get("message", "")) > 50 else event.get("message", ""),
            event.get("process_name", ""),
            f"{event.get('relevance_score', 0):.3f}"
        )
    
    console.print(table)
    
    if len(events) > 20:
        console.print(f"[dim]... and {len(events) - 20} more events[/dim]")


def _display_active_timelines(timelines: list) -> None:
    """Display active timeline events."""
    if not timelines:
        console.print(f"\n[yellow]⏱️  No active timeline events found[/yellow]")
        return
    
    console.print(f"\n[bold blue]⏱️  Active Timeline Events ({len(timelines)})[/bold blue]")
    
    for timeline_data in timelines:
        timeline_event = timeline_data.get("timeline_event", {})
        markers = timeline_data.get("relevant_markers", [])
        
        console.print(f"\n[bold]{timeline_event.get('name', 'Unknown')}[/bold] "
                     f"[cyan]({timeline_event.get('id', 'Unknown')})[/cyan]")
        console.print(f"  • Position: {timeline_event.get('position_id', 'Unknown')}")
        console.print(f"  • Start: {timeline_event.get('start_time', 'Unknown')}")
        console.print(f"  • End: {timeline_event.get('end_time', 'Ongoing')}")
        console.print(f"  • Complete: {timeline_event.get('is_complete', False)}")
        
        if markers:
            console.print(f"  • Relevant Markers ({len(markers)}):")
            for marker in markers[:5]:  # Show first 5 markers
                console.print(f"    - {marker.get('name', 'Unknown')} at {marker.get('timestamp', 'Unknown')}")
            if len(markers) > 5:
                console.print(f"    ... and {len(markers) - 5} more markers")
        else:
            console.print(f"  • No relevant markers in investigation window")


def _display_resource_snapshot(snapshot: dict) -> None:
    """Display resource snapshot."""
    if not snapshot or snapshot.get("error"):
        console.print(f"\n[yellow]💾 Resource snapshot unavailable[/yellow]")
        return
    
    console.print(f"\n[bold green]💾 Resource Snapshot[/bold green]")
    console.print(f"  • Timestamp: {snapshot.get('timestamp', 'Unknown')}")
    
    if snapshot.get("cpu_usage_percent") is not None:
        cpu = snapshot["cpu_usage_percent"]
        cpu_style = "red" if cpu > 80 else "yellow" if cpu > 60 else "green"
        console.print(f"  • CPU Usage: [{cpu_style}]{cpu:.1f}%[/{cpu_style}]")
    
    if snapshot.get("memory_usage_percent") is not None:
        memory = snapshot["memory_usage_percent"]
        mem_style = "red" if memory > 80 else "yellow" if memory > 60 else "green"
        console.print(f"  • Memory Usage: [{mem_style}]{memory:.1f}%[/{mem_style}]")
    
    if snapshot.get("disk_usage_percent") is not None:
        disk = snapshot["disk_usage_percent"]
        disk_style = "red" if disk > 90 else "yellow" if disk > 70 else "green"
        console.print(f"  • Disk Usage: [{disk_style}]{disk:.1f}%[/{disk_style}]")


def _display_concurrent_activity(activity: list) -> None:
    """Display concurrent activity across positions."""
    if not activity:
        console.print(f"\n[yellow]🔀 No concurrent activity found[/yellow]")
        return
    
    console.print(f"\n[bold magenta]🔀 Concurrent Activity ({len(activity)} positions)[/bold magenta]")
    
    for pos_activity in activity:
        position = pos_activity.get("position", "Unknown")
        events = pos_activity.get("events", [])
        
        console.print(f"\n[bold]{position}[/bold] ({len(events)} events)")
        
        for event in events[:3]:  # Show first 3 events per position
            console.print(f"  • {event.get('log_level', 'INFO')}: {event.get('log_event', 'Unknown')} "
                         f"at {event.get('timestamp', 'Unknown')[:19]}")
        
        if len(events) > 3:
            console.print(f"  ... and {len(events) - 3} more events")


def _display_recent_anomalies(anomalies: list) -> None:
    """Display recent anomalies."""
    if not anomalies:
        console.print(f"\n[yellow]📈 No recent anomalies detected[/yellow]")
        return
    
    console.print(f"\n[bold orange]📈 Recent Anomalies ({len(anomalies)})[/bold orange]")
    
    for anomaly in anomalies:
        severity = anomaly.get("severity", "medium")
        severity_style = "red" if severity == "high" else "yellow" if severity == "medium" else "blue"
        
        console.print(f"  • [{severity_style}]{anomaly.get('type', 'Unknown')}[/{severity_style}] "
                     f"in {anomaly.get('column', 'Unknown')}")
        console.print(f"    Value: {anomaly.get('value', 'Unknown')} "
                     f"(threshold: {anomaly.get('threshold', 'Unknown')})")
        console.print(f"    Time: {anomaly.get('start_time', 'Unknown')}")


def _display_event_bursts(bursts: list) -> None:
    """Display event bursts."""
    if not bursts:
        console.print("  [green]No event bursts detected[/green]")
        return
    
    console.print(f"  [bold]Found {len(bursts)} event burst(s)[/bold]")
    
    table = Table()
    table.add_column("Start Time", style="green")
    table.add_column("Duration", style="yellow")
    table.add_column("Events", style="red")
    table.add_column("Rate", style="magenta")
    table.add_column("Positions", style="cyan")
    
    for burst in bursts:
        duration = burst.get("duration_minutes", 0)
        event_count = burst.get("event_count", 0)
        events_per_min = burst.get("events_per_minute", 0)
        positions = burst.get("affected_positions", [])
        
        table.add_row(
            burst.get("start_time", "")[:19],
            f"{duration:.1f}m",
            str(event_count),
            f"{events_per_min:.1f}/min",
            ", ".join(positions[:3]) + ("..." if len(positions) > 3 else "")
        )
    
    console.print(table)


if __name__ == "__main__":
    app()
