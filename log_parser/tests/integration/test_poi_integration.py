"""Integration tests for Points of Interest Analyzer with real data."""

import pytest
from datetime import datetime
from pathlib import Path

from log_parser import LogParser
from log_parser.analysis.points_of_interest_analyzer import PointsOfInterestAnalyzer
from log_parser.analysis.cross_analyzer import CrossAnalyzer


# Test data path
TEST_DATA_PATH = Path("/Users/<USER>/Workspace/examples/logs-P2I-00147")


@pytest.mark.skipif(not TEST_DATA_PATH.exists(), reason="Test data not available")
class TestPOIIntegrationWithRealData:
    """Integration tests using real log data."""

    @pytest.fixture(scope="class")
    def log_parser(self):
        """Create log parser with real test data."""
        if not TEST_DATA_PATH.exists():
            pytest.skip("Test data directory not found")
        
        parser = LogParser()
        parser.parse_directory(str(TEST_DATA_PATH))
        return parser

    @pytest.fixture(scope="class")
    def analyzers(self, log_parser):
        """Create analyzers from real data."""
        return log_parser.get_analyzers()

    @pytest.fixture(scope="class")
    def cross_analyzer(self, analyzers):
        """Create cross analyzer."""
        structured_analyzer = analyzers.get("minknow")
        resource_analyzer = analyzers.get("resource")
        
        if not structured_analyzer or not resource_analyzer:
            pytest.skip("Required analyzers not available")
            
        return CrossAnalyzer(resource_analyzer, structured_analyzer)

    @pytest.fixture(scope="class")
    def poi_analyzer(self, analyzers, cross_analyzer):
        """Create POI analyzer with real data."""
        structured_analyzer = analyzers.get("minknow")
        resource_analyzer = analyzers.get("resource")
        timeline_analyzer = analyzers.get("timeline")
        
        if not all([structured_analyzer, resource_analyzer, timeline_analyzer]):
            pytest.skip("Required analyzers not available")
            
        return PointsOfInterestAnalyzer(
            structured_analyzer=structured_analyzer,
            resource_analyzer=resource_analyzer,
            timeline_analyzer=timeline_analyzer,
            cross_analyzer=cross_analyzer
        )

    def test_poi_analyzer_creation(self, poi_analyzer):
        """Test that POI analyzer can be created with real data."""
        assert poi_analyzer.analyzer_type == "points_of_interest"
        assert hasattr(poi_analyzer, 'structured_analyzer')
        assert hasattr(poi_analyzer, 'resource_analyzer')
        assert hasattr(poi_analyzer, 'timeline_analyzer')
        assert hasattr(poi_analyzer, 'cross_analyzer')

    def test_get_common_time_range(self, cross_analyzer):
        """Test that we can get common time range from real data."""
        start, end = cross_analyzer.get_common_time_range()
        assert start is not None
        assert end is not None
        assert start < end
        print(f"\nCommon time range: {start} to {end}")

    def test_investigation_context_with_real_data(self, poi_analyzer, cross_analyzer):
        """Test investigation context generation with real data."""
        # Get a time in the middle of the data range
        start, end = cross_analyzer.get_common_time_range()
        if not start or not end:
            pytest.skip("No common time range available")
            
        # Use middle of time range as target
        middle_time = start + (end - start) / 2
        
        print(f"\nInvestigating time: {middle_time}")
        
        context = poi_analyzer.get_investigation_context(
            target_time=middle_time,
            window_minutes=60
        )
        
        # Verify structure
        assert "target_time" in context
        assert "window" in context
        assert "critical_events" in context
        assert "active_timelines" in context
        assert "resource_snapshot" in context
        assert "concurrent_activity" in context
        assert "recent_anomalies" in context
        
        # Print summary for manual verification
        print(f"Critical events found: {len(context['critical_events'])}")
        print(f"Active timelines found: {len(context['active_timelines'])}")
        print(f"Concurrent activity positions: {len(context['concurrent_activity'])}")
        print(f"Recent anomalies found: {len(context['recent_anomalies'])}")
        
        # Verify types
        assert isinstance(context["critical_events"], list)
        assert isinstance(context["active_timelines"], list)
        assert isinstance(context["resource_snapshot"], dict)
        assert isinstance(context["concurrent_activity"], list)
        assert isinstance(context["recent_anomalies"], list)

    def test_event_burst_detection_with_real_data(self, poi_analyzer, cross_analyzer):
        """Test event burst detection with real data."""
        start, end = cross_analyzer.get_common_time_range()
        if not start or not end:
            pytest.skip("No common time range available")
            
        print(f"\nDetecting bursts in range: {start} to {end}")
        
        bursts = poi_analyzer.detect_event_bursts(
            window_minutes=5,
            threshold_count=3,
            start_time=start,
            end_time=end
        )
        
        print(f"Event bursts detected: {len(bursts)}")
        
        # Verify structure
        assert isinstance(bursts, list)
        
        # If we found bursts, verify their structure
        for i, burst in enumerate(bursts[:3]):  # Check first 3 bursts
            print(f"Burst {i+1}: {burst.get('start_time')} to {burst.get('end_time')}, "
                  f"{burst.get('event_count')} events, "
                  f"{burst.get('events_per_minute', 0):.1f} events/min")
            
            assert "start_time" in burst
            assert "end_time" in burst
            assert "event_count" in burst
            assert "events_per_minute" in burst

    def test_relevance_scoring_with_real_data(self, poi_analyzer, cross_analyzer):
        """Test relevance scoring with real data."""
        start, end = cross_analyzer.get_common_time_range()
        if not start or not end:
            pytest.skip("No common time range available")
            
        # Get investigation context first
        middle_time = start + (end - start) / 2
        context = poi_analyzer.get_investigation_context(
            target_time=middle_time,
            window_minutes=30
        )
        
        critical_events = context.get("critical_events", [])
        if not critical_events:
            pytest.skip("No critical events found for scoring test")
            
        print(f"\nScoring {len(critical_events)} critical events")
        
        # Calculate relevance scores
        scored_events = poi_analyzer.calculate_relevance_scores(critical_events, context)
        
        assert len(scored_events) == len(critical_events)
        
        # Verify all events have scores
        for event in scored_events:
            assert "relevance_score" in event
            assert 0 <= event["relevance_score"] <= 1
            
        # Verify events are sorted by relevance
        scores = [event["relevance_score"] for event in scored_events]
        assert scores == sorted(scores, reverse=True)
        
        # Print top 3 most relevant events
        for i, event in enumerate(scored_events[:3]):
            print(f"Event {i+1} (score: {event['relevance_score']:.3f}): "
                  f"{event.get('log_level')} - {event.get('log_event')} at {event.get('timestamp')}")

    def test_position_filtering(self, poi_analyzer, cross_analyzer):
        """Test position filtering in investigation context."""
        # Get available positions
        positions = poi_analyzer.structured_analyzer.get_positions()
        if not positions:
            pytest.skip("No positions available")
            
        print(f"\nAvailable positions: {positions}")
        
        # Use first position for testing
        test_position = positions[0]
        
        start, end = cross_analyzer.get_common_time_range()
        if not start or not end:
            pytest.skip("No common time range available")
            
        middle_time = start + (end - start) / 2
        
        # Get context with position filter
        context = poi_analyzer.get_investigation_context(
            target_time=middle_time,
            window_minutes=60,
            position=test_position
        )
        
        print(f"Investigation for position {test_position}:")
        print(f"  Critical events: {len(context['critical_events'])}")
        print(f"  Active timelines: {len(context['active_timelines'])}")
        
        # Verify position filter was applied
        assert context["position"] == test_position
        
        # Verify timeline events are for correct position (if any)
        for timeline_data in context["active_timelines"]:
            timeline_event = timeline_data["timeline_event"]
            assert timeline_event["position_id"] == test_position

    def test_error_handling_with_real_data(self, poi_analyzer):
        """Test error handling with invalid inputs on real data."""
        # Test with invalid time (far in future)
        future_time = datetime(2030, 1, 1, 12, 0, 0)
        
        context = poi_analyzer.get_investigation_context(future_time)
        
        # Should handle gracefully, returning empty results
        assert context["target_time"] == future_time
        assert isinstance(context["critical_events"], list)
        assert isinstance(context["active_timelines"], list)
        
        print(f"\nFuture time test results:")
        print(f"  Critical events: {len(context['critical_events'])}")
        print(f"  Active timelines: {len(context['active_timelines'])}")