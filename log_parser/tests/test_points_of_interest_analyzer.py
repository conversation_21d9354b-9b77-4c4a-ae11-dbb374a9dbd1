"""Tests for Points of Interest Analyzer."""

import pytest
from datetime import datetime, timedelta
from unittest.mock import <PERSON><PERSON>, MagicMock

from log_parser.analysis.points_of_interest_analyzer import PointsOfInterestAnalyzer
from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
from log_parser.analysis.resource_analyzer import ResourceAnalyzer
from log_parser.analysis.timeline_analyzer import TimelineAnalyzer, TimelineEvent, TimelineMarker
from log_parser.analysis.cross_analyzer import CrossAnalyzer


@pytest.fixture
def mock_structured_analyzer():
    """Create mock structured log analyzer."""
    mock = Mock(spec=StructuredLogAnalyzer)
    mock.get_log_level_counts.return_value = {"ERROR": 2, "WARNING": 1}
    mock.get_positions.return_value = ["PAE12345", "PAE67890"]
    
    # Mock query method to return empty DataFrame-like object
    mock_df = Mock()
    mock_df.empty = False
    mock_df.iterrows.return_value = [
        (datetime(2023, 1, 1, 14, 30), {
            "log_level": "ERROR", 
            "log_event": "test_error",
            "message": "Test error message",
            "process_name": "test_process"
        })
    ]
    mock.query.return_value = mock_df
    
    return mock


@pytest.fixture
def mock_resource_analyzer():
    """Create mock resource analyzer."""
    mock = Mock(spec=ResourceAnalyzer)
    mock.get_system_overview.return_value = {
        "cpu_usage_percent": 75.5,
        "memory_usage_percent": 68.2,
        "disk_usage_percent": 45.1
    }
    return mock


@pytest.fixture
def mock_timeline_analyzer():
    """Create mock timeline analyzer."""
    mock = Mock(spec=TimelineAnalyzer)
    
    # Create mock timeline event
    mock_timeline_event = Mock(spec=TimelineEvent)
    mock_timeline_event.id = "test_event_123"
    mock_timeline_event.name = "sequencing_run"
    mock_timeline_event.start_time = datetime(2023, 1, 1, 14, 0)
    mock_timeline_event.end_time = None
    mock_timeline_event.position_id = "PAE12345"
    mock_timeline_event.metadata = {"protocol": "test_protocol"}
    mock_timeline_event.is_complete.return_value = False
    
    mock.query_timeline_events.return_value = [mock_timeline_event]
    
    # Create mock marker
    mock_marker = Mock(spec=TimelineMarker)
    mock_marker.id = "test_marker_456"
    mock_marker.name = "pore_scan_complete"
    mock_marker.timestamp = datetime(2023, 1, 1, 14, 30)
    mock_marker.metadata = {"channel_count": 4000}
    mock_marker.log_event = "pore_scan_complete"
    
    mock.query_markers.return_value = [mock_marker]
    
    return mock


@pytest.fixture
def mock_cross_analyzer():
    """Create mock cross analyzer."""
    mock = Mock(spec=CrossAnalyzer)
    mock.get_common_time_range.return_value = (
        datetime(2023, 1, 1, 10, 0),
        datetime(2023, 1, 1, 16, 0)
    )
    return mock


@pytest.fixture
def poi_analyzer(mock_structured_analyzer, mock_resource_analyzer, 
                 mock_timeline_analyzer, mock_cross_analyzer):
    """Create POI analyzer with mocked dependencies."""
    return PointsOfInterestAnalyzer(
        structured_analyzer=mock_structured_analyzer,
        resource_analyzer=mock_resource_analyzer,
        timeline_analyzer=mock_timeline_analyzer,
        cross_analyzer=mock_cross_analyzer
    )


class TestPointsOfInterestAnalyzer:
    """Test cases for PointsOfInterestAnalyzer."""

    def test_initialization_success(self, poi_analyzer):
        """Test successful initialization of POI analyzer."""
        assert poi_analyzer.analyzer_type == "points_of_interest"
        assert isinstance(poi_analyzer.structured_analyzer, Mock)
        assert isinstance(poi_analyzer.resource_analyzer, Mock)
        assert isinstance(poi_analyzer.timeline_analyzer, Mock)
        assert isinstance(poi_analyzer.cross_analyzer, Mock)

    def test_initialization_validation_failure(self, mock_resource_analyzer, 
                                              mock_timeline_analyzer, mock_cross_analyzer):
        """Test initialization fails with invalid analyzer types."""
        with pytest.raises(ValueError, match="structured_analyzer must be an instance"):
            PointsOfInterestAnalyzer(
                structured_analyzer="invalid",  # Not a proper analyzer
                resource_analyzer=mock_resource_analyzer,
                timeline_analyzer=mock_timeline_analyzer,
                cross_analyzer=mock_cross_analyzer
            )

    def test_get_investigation_context_basic(self, poi_analyzer):
        """Test basic investigation context generation."""
        target_time = datetime(2023, 1, 1, 14, 30)
        
        context = poi_analyzer.get_investigation_context(target_time, window_minutes=60)
        
        # Verify basic structure
        assert context["target_time"] == target_time
        assert "window" in context
        assert context["window"]["start"] == target_time - timedelta(minutes=30)
        assert context["window"]["end"] == target_time + timedelta(minutes=30)
        assert context["position"] is None
        
        # Verify all sections are present
        assert "critical_events" in context
        assert "active_timelines" in context
        assert "resource_snapshot" in context
        assert "concurrent_activity" in context
        assert "recent_anomalies" in context
        
        # Verify some content was collected
        assert isinstance(context["critical_events"], list)
        assert isinstance(context["active_timelines"], list)
        assert isinstance(context["resource_snapshot"], dict)

    def test_get_investigation_context_with_position(self, poi_analyzer):
        """Test investigation context with position filter."""
        target_time = datetime(2023, 1, 1, 14, 30)
        position = "PAE12345"
        
        context = poi_analyzer.get_investigation_context(
            target_time, window_minutes=120, position=position
        )
        
        assert context["target_time"] == target_time
        assert context["position"] == position
        assert context["window"]["start"] == target_time - timedelta(minutes=60)
        assert context["window"]["end"] == target_time + timedelta(minutes=60)

    def test_collect_critical_events(self, poi_analyzer):
        """Test critical events collection."""
        start = datetime(2023, 1, 1, 14, 0)
        end = datetime(2023, 1, 1, 15, 0)
        
        critical_events = poi_analyzer._collect_critical_events(start, end, None)
        
        # Verify structured analyzer was called correctly
        poi_analyzer.structured_analyzer.get_log_level_counts.assert_called_once_with(
            start=start, end=end, position=None
        )
        
        # Verify events were collected
        assert isinstance(critical_events, list)
        # Should have called query for ERROR level (since mock returns ERROR: 2)
        poi_analyzer.structured_analyzer.query.assert_called()

    def test_collect_active_timeline_events(self, poi_analyzer):
        """Test active timeline events collection."""
        target_time = datetime(2023, 1, 1, 14, 30)
        window_start = datetime(2023, 1, 1, 14, 0)
        window_end = datetime(2023, 1, 1, 15, 0)
        
        active_timelines = poi_analyzer._collect_active_timeline_events(
            target_time, window_start, window_end, None
        )
        
        # Verify timeline analyzer was called correctly
        poi_analyzer.timeline_analyzer.query_timeline_events.assert_called_once_with(
            position_id=None,
            start_time=target_time,
            end_time=target_time,
            include_children=True
        )
        
        # Verify markers were queried for timeline events
        poi_analyzer.timeline_analyzer.query_markers.assert_called()
        
        # Verify structure
        assert isinstance(active_timelines, list)
        if active_timelines:  # If mock returned data
            timeline_data = active_timelines[0]
            assert "timeline_event" in timeline_data
            assert "relevant_markers" in timeline_data

    def test_collect_resource_snapshot(self, poi_analyzer):
        """Test resource snapshot collection."""
        target_time = datetime(2023, 1, 1, 14, 30)
        window_start = datetime(2023, 1, 1, 14, 0)
        window_end = datetime(2023, 1, 1, 15, 0)
        
        snapshot = poi_analyzer._collect_resource_snapshot(target_time, window_start, window_end)
        
        # Verify resource analyzer was called
        poi_analyzer.resource_analyzer.get_system_overview.assert_called_once_with(
            start=window_start, end=window_end
        )
        
        # Verify snapshot structure
        assert isinstance(snapshot, dict)
        assert "timestamp" in snapshot
        assert snapshot["timestamp"] == target_time

    def test_detect_event_bursts_basic(self, poi_analyzer):
        """Test basic event burst detection."""
        start_time = datetime(2023, 1, 1, 10, 0)
        end_time = datetime(2023, 1, 1, 16, 0)
        
        bursts = poi_analyzer.detect_event_bursts(
            window_minutes=5,
            threshold_count=3,
            start_time=start_time,
            end_time=end_time
        )
        
        # Verify structured analyzer was queried
        poi_analyzer.structured_analyzer.query.assert_called()
        
        # Verify return type
        assert isinstance(bursts, list)

    def test_detect_event_bursts_no_time_range(self, poi_analyzer):
        """Test event burst detection without explicit time range."""
        bursts = poi_analyzer.detect_event_bursts(window_minutes=5, threshold_count=3)
        
        # Should call cross analyzer for common time range
        poi_analyzer.cross_analyzer.get_common_time_range.assert_called_once()
        
        assert isinstance(bursts, list)

    def test_calculate_relevance_scores(self, poi_analyzer):
        """Test relevance scoring calculation."""
        target_time = datetime(2023, 1, 1, 14, 30)
        
        # Create test events
        events = [
            {
                "timestamp": target_time,  # Exact match
                "log_level": "ERROR",
                "log_event": "test_error"
            },
            {
                "timestamp": target_time + timedelta(minutes=30),  # 30 min later
                "log_level": "WARNING", 
                "log_event": "test_warning"
            },
            {
                "timestamp": target_time - timedelta(hours=2),  # 2 hours earlier
                "log_level": "INFO",
                "log_event": "test_info"
            }
        ]
        
        investigation_context = {"target_time": target_time}
        
        scored_events = poi_analyzer.calculate_relevance_scores(events, investigation_context)
        
        # Verify all events have relevance scores
        assert len(scored_events) == 3
        for event in scored_events:
            assert "relevance_score" in event
            assert 0 <= event["relevance_score"] <= 1
        
        # Verify events are sorted by relevance (highest first)
        scores = [event["relevance_score"] for event in scored_events]
        assert scores == sorted(scores, reverse=True)
        
        # ERROR at target time should have highest score
        highest_scored = scored_events[0]
        assert highest_scored["log_level"] == "ERROR"
        assert highest_scored["timestamp"] == target_time

    def test_error_handling_in_investigation_context(self, poi_analyzer):
        """Test error handling in investigation context generation."""
        # Make all analyzers throw exceptions to trigger main error handler
        poi_analyzer.structured_analyzer.get_log_level_counts.side_effect = Exception("Test error")
        poi_analyzer.timeline_analyzer.query_timeline_events.side_effect = Exception("Timeline error")
        poi_analyzer.resource_analyzer.get_system_overview.side_effect = Exception("Resource error")
        
        target_time = datetime(2023, 1, 1, 14, 30)
        context = poi_analyzer.get_investigation_context(target_time)
        
        # Should return partial context with error information when multiple failures occur
        # In this case, errors in sub-methods are handled gracefully, returning empty lists
        assert context["target_time"] == target_time
        # Should still have empty lists for sections that failed
        assert context["critical_events"] == []
        assert context["active_timelines"] == []

    def test_empty_data_handling(self, poi_analyzer):
        """Test handling of empty data from analyzers."""
        # Mock empty responses
        poi_analyzer.structured_analyzer.get_log_level_counts.return_value = {}
        mock_empty_df = Mock()
        mock_empty_df.empty = True
        mock_empty_df.iterrows.return_value = []
        poi_analyzer.structured_analyzer.query.return_value = mock_empty_df
        
        target_time = datetime(2023, 1, 1, 14, 30)
        context = poi_analyzer.get_investigation_context(target_time)
        
        # Should handle empty data gracefully
        assert isinstance(context["critical_events"], list)
        assert len(context["critical_events"]) == 0

    def test_concurrent_activity_excludes_position(self, poi_analyzer):
        """Test that concurrent activity excludes the investigated position."""
        target_time = datetime(2023, 1, 1, 14, 30)
        excluded_position = "PAE12345"
        
        concurrent_activity = poi_analyzer._collect_concurrent_activity(target_time, excluded_position)
        
        # Verify positions were queried
        poi_analyzer.structured_analyzer.get_positions.assert_called_once()
        
        # Verify structured analyzer query was called (for non-excluded positions)
        poi_analyzer.structured_analyzer.query.assert_called()
        
        assert isinstance(concurrent_activity, list)