"""Points of Interest Analyzer for orchestrating investigation-focused log analysis."""

from __future__ import annotations

import logging
from datetime import datetime
from typing import Any

from .cross_analyzer import CrossAnalyzer
from .resource_analyzer import ResourceAnalyzer
from .structured_log_analyzer import StructuredLogAnalyzer
from .timeline_analyzer import TimelineAnalyzer

logger = logging.getLogger(__name__)


class PointsOfInterestAnalyzer:
    """Orchestration layer for investigation-focused log analysis.
    
    This analyzer combines data from multiple existing analyzers to provide
    contextual information for incident investigation workflows. It does not
    implement new analysis algorithms but focuses on intelligent data
    aggregation and relevance scoring.
    """

    def __init__(
        self,
        structured_analyzer: StructuredLogAnalyzer,
        resource_analyzer: ResourceAnalyzer,
        timeline_analyzer: TimelineAnalyzer,
        cross_analyzer: CrossAnalyzer,
    ):
        """Initialize POI analyzer with existing analyzers.

        Args:
            structured_analyzer: Analyzer for MinKNOW structured logs
            resource_analyzer: Analyzer for system resource metrics
            timeline_analyzer: Analyzer for timeline events and markers
            cross_analyzer: Cross-log correlation analyzer
        """
        logger.debug("POI_ANALYZER_INIT_START: Initializing PointsOfInterestAnalyzer")

        self.structured_analyzer = structured_analyzer
        self.resource_analyzer = resource_analyzer
        self.timeline_analyzer = timeline_analyzer
        self.cross_analyzer = cross_analyzer

        # Validate that all required analyzers are provided
        self._validate_analyzers()

        logger.debug("POI_ANALYZER_INIT_END: PointsOfInterestAnalyzer initialized successfully")

    @property
    def analyzer_type(self) -> str:
        """Return the type of analyzer."""
        return "points_of_interest"

    def _validate_analyzers(self) -> None:
        """Validate that all required analyzers are provided and functional."""
        required_analyzers = {
            "structured_analyzer": StructuredLogAnalyzer,
            "resource_analyzer": ResourceAnalyzer,
            "timeline_analyzer": TimelineAnalyzer,
            "cross_analyzer": CrossAnalyzer,
        }

        for attr_name, expected_type in required_analyzers.items():
            analyzer = getattr(self, attr_name)
            if not isinstance(analyzer, expected_type):
                raise ValueError(f"{attr_name} must be an instance of {expected_type.__name__}")

        logger.debug("POI_ANALYZER_VALIDATION: All required analyzers validated successfully")

    def get_investigation_context(
        self, target_time: datetime, window_minutes: int = 60, position: str | None = None
    ) -> dict[str, Any]:
        """Get comprehensive investigation context around a specific time.

        This is the primary method for investigation workflows. It orchestrates
        calls to existing analyzers to provide all relevant context around a
        target time for incident investigation.

        Args:
            target_time: The focus point for investigation
            window_minutes: Time window around target (total window, will be split ±)
            position: Optional position filter for spatial context

        Returns:
            Dictionary containing investigation context with:
            - target_time: The investigation focus time
            - window: Start and end of investigation window
            - position: Position filter if specified
            - critical_events: ERROR/WARNING events within window
            - active_timelines: Timeline events spanning target time
            - resource_snapshot: System metrics at target time
            - concurrent_activity: Cross-position events at same time
            - recent_anomalies: Statistical anomalies in expanded window
        """
        logger.info("POI_INVESTIGATION_START: Building context for %s (±%d min, position=%s)", 
                    target_time, window_minutes, position)

        try:
            # Calculate investigation window boundaries (split the window around target time)
            from datetime import timedelta
            half_window = timedelta(minutes=window_minutes // 2)
            window_start = target_time - half_window
            window_end = target_time + half_window

            logger.debug("POI_INVESTIGATION_WINDOW: %s to %s", window_start, window_end)

            # Initialize investigation context
            context = {
                "target_time": target_time,
                "window": {"start": window_start, "end": window_end},
                "position": position,
                "critical_events": [],
                "active_timelines": [],
                "resource_snapshot": {},
                "concurrent_activity": [],
                "recent_anomalies": [],
            }

            # Collect critical events (ERROR/WARNING within investigation window)
            context["critical_events"] = self._collect_critical_events(window_start, window_end, position)

            # Collect active timeline events and associated markers
            context["active_timelines"] = self._collect_active_timeline_events(target_time, window_start, window_end, position)

            # Collect resource snapshot at target time
            context["resource_snapshot"] = self._collect_resource_snapshot(target_time, window_start, window_end)

            # Collect concurrent activity across positions
            context["concurrent_activity"] = self._collect_concurrent_activity(target_time, position)

            # Collect recent anomalies (with configurable expanded window)
            context["recent_anomalies"] = self._collect_recent_anomalies(window_start, window_end, position)

            logger.info("POI_INVESTIGATION_END: Context built successfully - %d critical events, %d active timelines",
                        len(context["critical_events"]), len(context["active_timelines"]))

            return context

        except Exception as e:
            logger.exception("POI_INVESTIGATION_ERROR: Failed to build investigation context")
            # Return partial context with error information
            return {
                "target_time": target_time,
                "window": {"start": window_start, "end": window_end},
                "position": position,
                "error": str(e),
                "critical_events": [],
                "active_timelines": [],
                "resource_snapshot": {},
                "concurrent_activity": [],
                "recent_anomalies": [],
            }

    def _collect_critical_events(self, start: datetime, end: datetime, position: str | None) -> list[dict[str, Any]]:
        """Collect ERROR/WARNING events within investigation window."""
        try:
            logger.debug("POI_CRITICAL_EVENTS_START: Collecting for %s to %s, position=%s", start, end, position)

            critical_events = []

            # Get ERROR and WARNING level counts
            error_counts = self.structured_analyzer.get_log_level_counts(
                start=start, end=end, position=position
            )

            # Query for actual ERROR and WARNING events
            for level in ["ERROR", "WARNING"]:
                if level in error_counts and error_counts[level] > 0:
                    level_events = self.structured_analyzer.query(
                        start=start, end=end, log_level=level, folder_name=position
                    )

                    for _, event in level_events.iterrows():
                        critical_events.append({
                            "timestamp": event.name,  # Index is timestamp
                            "log_level": event.get("log_level"),
                            "log_event": event.get("log_event"),
                            "message": event.get("message", ""),
                            "process_name": event.get("process_name"),
                            "relevance_score": 0.0,  # Will be calculated later
                        })

            # Sort by timestamp
            critical_events.sort(key=lambda x: x["timestamp"])

            logger.debug("POI_CRITICAL_EVENTS_END: Found %d critical events", len(critical_events))
            return critical_events

        except Exception as e:
            logger.exception("POI_CRITICAL_EVENTS_ERROR: Failed to collect critical events")
            return []

    def _collect_active_timeline_events(
        self, target_time: datetime, window_start: datetime, window_end: datetime, position: str | None
    ) -> list[dict[str, Any]]:
        """Collect timeline events active during target time with associated markers."""
        try:
            logger.debug("POI_TIMELINE_EVENTS_START: Collecting for target %s, position=%s", target_time, position)

            active_timelines = []

            # Query for timeline events that span, start, or end during target time
            # This gives broader context than just the investigation window
            timeline_events = self.timeline_analyzer.query_timeline_events(
                position_id=position,
                start_time=target_time,  # Events active at target time
                end_time=target_time,
                include_children=True
            )

            for timeline_event in timeline_events:
                # For each timeline event, get markers within the investigation window
                relevant_markers = self.timeline_analyzer.query_markers(
                    timeline_event_id=timeline_event.id,
                    start_time=window_start,
                    end_time=window_end
                )

                # Create timeline event data structure
                timeline_data = {
                    "timeline_event": {
                        "id": timeline_event.id,
                        "name": timeline_event.name,
                        "start_time": timeline_event.start_time,
                        "end_time": timeline_event.end_time,
                        "position_id": timeline_event.position_id,
                        "metadata": timeline_event.metadata,
                        "is_complete": timeline_event.is_complete(),
                    },
                    "relevant_markers": []
                }

                # Add markers that occurred within investigation window
                for marker in relevant_markers:
                    timeline_data["relevant_markers"].append({
                        "id": marker.id,
                        "name": marker.name,
                        "timestamp": marker.timestamp,
                        "metadata": marker.metadata,
                        "log_event": marker.log_event,
                    })

                active_timelines.append(timeline_data)

            # Sort by timeline event start time
            active_timelines.sort(key=lambda x: x["timeline_event"]["start_time"])

            logger.debug("POI_TIMELINE_EVENTS_END: Found %d active timeline events", len(active_timelines))
            return active_timelines

        except Exception as e:
            logger.exception("POI_TIMELINE_EVENTS_ERROR: Failed to collect timeline events")
            return []

    def _collect_resource_snapshot(self, target_time: datetime, window_start: datetime, window_end: datetime) -> dict[str, Any]:
        """Collect system resource metrics at target time."""
        try:
            logger.debug("POI_RESOURCE_SNAPSHOT_START: Collecting for target %s", target_time)

            # Get system overview for the investigation window
            system_overview = self.resource_analyzer.get_system_overview(
                start=window_start, end=window_end
            )

            # Try to get specific metrics at target time
            snapshot = {
                "timestamp": target_time,
                "cpu_usage_percent": None,
                "memory_usage_percent": None,
                "disk_usage_percent": None,
            }

            # Extract key metrics from system overview
            if system_overview:
                # System overview typically contains aggregated metrics
                # Use the overview data as our snapshot
                for key in ["cpu_usage_percent", "memory_usage_percent", "disk_usage_percent"]:
                    if key in system_overview:
                        snapshot[key] = system_overview[key]

            logger.debug("POI_RESOURCE_SNAPSHOT_END: Collected snapshot with %d metrics", 
                        len([k for k, v in snapshot.items() if v is not None and k != "timestamp"]))
            return snapshot

        except Exception as e:
            logger.exception("POI_RESOURCE_SNAPSHOT_ERROR: Failed to collect resource snapshot")
            return {"timestamp": target_time, "error": str(e)}

    def _collect_concurrent_activity(self, target_time: datetime, excluded_position: str | None) -> list[dict[str, Any]]:
        """Collect events happening on other positions at the same time."""
        try:
            logger.debug("POI_CONCURRENT_ACTIVITY_START: Collecting for target %s, excluding position %s", 
                        target_time, excluded_position)

            # Use a small tolerance window around target time for "concurrent"
            from datetime import timedelta
            tolerance = timedelta(seconds=30)
            tolerance_start = target_time - tolerance
            tolerance_end = target_time + tolerance

            concurrent_activity = []

            # Get all positions
            all_positions = self.structured_analyzer.get_positions()

            for position in all_positions:
                # Skip the position being investigated
                if excluded_position and position == excluded_position:
                    continue

                # Query for events in tolerance window for this position
                position_events = self.structured_analyzer.query(
                    start=tolerance_start,
                    end=tolerance_end,
                    folder_name=position
                )

                if not position_events.empty:
                    events = []
                    for _, event in position_events.iterrows():
                        events.append({
                            "timestamp": event.name,
                            "log_event": event.get("log_event"),
                            "log_level": event.get("log_level"),
                            "process_name": event.get("process_name"),
                        })

                    if events:
                        concurrent_activity.append({
                            "position": position,
                            "events": events
                        })

            logger.debug("POI_CONCURRENT_ACTIVITY_END: Found activity on %d positions", len(concurrent_activity))
            return concurrent_activity

        except Exception as e:
            logger.exception("POI_CONCURRENT_ACTIVITY_ERROR: Failed to collect concurrent activity")
            return []

    def _collect_recent_anomalies(self, window_start: datetime, window_end: datetime, position: str | None) -> list[dict[str, Any]]:
        """Collect statistical anomalies in expanded time window."""
        try:
            logger.debug("POI_ANOMALIES_START: Collecting for window %s to %s, position=%s", 
                        window_start, window_end, position)

            anomalies = []

            # Expand window for anomaly detection (configurable - start with 2x)
            from datetime import timedelta
            expansion = (window_end - window_start) / 2
            expanded_start = window_start - expansion
            expanded_end = window_end + expansion

            # Get log level anomalies from structured analyzer
            try:
                log_anomalies = self.structured_analyzer.detect_log_level_anomalies(
                    start=expanded_start,
                    end=expanded_end,
                    folder_name=position
                )

                for anomaly in log_anomalies:
                    anomalies.append({
                        "type": "log_level_anomaly",
                        "column": f"log_level_{anomaly.get('type', 'unknown')}",
                        "start_time": anomaly.get("start"),
                        "value": anomaly.get("rate", 0),
                        "threshold": anomaly.get("threshold", 0),
                        "severity": "high" if anomaly.get("type") == "error" else "medium",
                    })

            except Exception as e:
                logger.warning("POI_ANOMALIES_LOG_ERROR: Failed to get log anomalies: %s", e)

            # Get resource anomalies (example for CPU usage)
            try:
                # This is a placeholder - actual implementation would query specific metrics
                # For now, just check if resource analyzer has anomaly detection capabilities
                if hasattr(self.resource_analyzer, 'detect_time_series_anomalies'):
                    # Try to detect CPU usage anomalies as an example
                    cpu_anomalies = self.resource_analyzer.detect_time_series_anomalies(
                        column="cpu_usage_percent",
                        start=expanded_start,
                        end=expanded_end
                    )

                    for anomaly in cpu_anomalies:
                        anomalies.append({
                            "type": "resource_spike",
                            "column": "cpu_usage_percent",
                            "start_time": anomaly.get("start"),
                            "value": anomaly.get("value", 0),
                            "threshold": anomaly.get("threshold", 0),
                            "severity": "high" if anomaly.get("value", 0) > 90 else "medium",
                        })

            except Exception as e:
                logger.warning("POI_ANOMALIES_RESOURCE_ERROR: Failed to get resource anomalies: %s", e)

            logger.debug("POI_ANOMALIES_END: Found %d anomalies", len(anomalies))
            return anomalies

        except Exception as e:
            logger.exception("POI_ANOMALIES_ERROR: Failed to collect anomalies")
            return []

    def detect_event_bursts(
        self,
        window_minutes: int = 5,
        threshold_count: int = 3,
        event_types: list[str] | None = None,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        position: str | None = None,
    ) -> list[dict[str, Any]]:
        """Detect rapid increases in event counts (event bursts).

        Args:
            window_minutes: Size of sliding window for burst detection
            threshold_count: Minimum events in window to trigger burst
            event_types: Optional filter for specific event types
            start_time: Start of analysis period
            end_time: End of analysis period
            position: Optional position filter

        Returns:
            List of burst periods with metadata
        """
        logger.info("POI_BURST_DETECTION_START: window=%d min, threshold=%d, period=%s to %s",
                    window_minutes, threshold_count, start_time, end_time)

        try:
            # Use cross analyzer to get common time range if not specified
            if not start_time or not end_time:
                common_start, common_end = self.cross_analyzer.get_common_time_range()
                start_time = start_time or common_start
                end_time = end_time or common_end

            if not start_time or not end_time:
                logger.warning("POI_BURST_DETECTION_WARNING: No valid time range available")
                return []

            # Query for events in the specified time range
            query_filters = {"folder_name": position} if position else {}
            if event_types:
                # Note: This would need to be adapted based on how event_types filtering works
                # in the structured analyzer - this is a placeholder
                pass

            events_df = self.structured_analyzer.query(
                start=start_time,
                end=end_time,
                **query_filters
            )

            if events_df.empty:
                logger.info("POI_BURST_DETECTION_END: No events found in time range")
                return []

            # Implement sliding window burst detection
            bursts = self._analyze_event_bursts(events_df, window_minutes, threshold_count, event_types)

            logger.info("POI_BURST_DETECTION_END: Found %d burst periods", len(bursts))
            return bursts

        except Exception as e:
            logger.exception("POI_BURST_DETECTION_ERROR: Failed to detect event bursts")
            return []

    def _analyze_event_bursts(
        self, events_df, window_minutes: int, threshold_count: int, event_types: list[str] | None
    ) -> list[dict[str, Any]]:
        """Analyze DataFrame for event bursts using sliding window."""
        import pandas as pd
        from datetime import timedelta

        bursts = []

        try:
            # Resample events into time windows
            window_freq = f"{window_minutes}min"
            event_counts = events_df.resample(window_freq).size()

            # Find windows exceeding threshold
            burst_windows = event_counts[event_counts >= threshold_count]

            if burst_windows.empty:
                return bursts

            # Group adjacent burst windows into burst periods
            burst_periods = []
            current_period_start = None
            current_period_end = None
            current_period_counts = []

            for window_time, count in burst_windows.items():
                if current_period_start is None:
                    # Start new burst period
                    current_period_start = window_time
                    current_period_end = window_time + timedelta(minutes=window_minutes)
                    current_period_counts = [count]
                else:
                    # Check if this window is adjacent to current period
                    expected_next = current_period_end
                    if window_time <= expected_next + timedelta(minutes=window_minutes):
                        # Extend current period
                        current_period_end = window_time + timedelta(minutes=window_minutes)
                        current_period_counts.append(count)
                    else:
                        # End current period and start new one
                        burst_periods.append((current_period_start, current_period_end, current_period_counts))
                        current_period_start = window_time
                        current_period_end = window_time + timedelta(minutes=window_minutes)
                        current_period_counts = [count]

            # Add final period if exists
            if current_period_start is not None:
                burst_periods.append((current_period_start, current_period_end, current_period_counts))

            # Create burst metadata
            for period_start, period_end, counts in burst_periods:
                duration_minutes = (period_end - period_start).total_seconds() / 60
                total_events = sum(counts)
                peak_intensity = max(counts)
                avg_events_per_minute = total_events / max(duration_minutes, 1)

                # Get event types and affected positions for this burst
                burst_events = events_df.loc[period_start:period_end]
                event_types_in_burst = burst_events.get("log_event", pd.Series()).unique().tolist()
                affected_positions = burst_events.get("folder_name", pd.Series()).unique().tolist()

                bursts.append({
                    "start_time": period_start,
                    "end_time": period_end,
                    "duration_minutes": duration_minutes,
                    "event_count": total_events,
                    "events_per_minute": avg_events_per_minute,
                    "event_types": event_types_in_burst,
                    "affected_positions": affected_positions,
                    "peak_intensity": peak_intensity,
                })

            return bursts

        except Exception as e:
            logger.exception("POI_BURST_ANALYSIS_ERROR: Failed to analyze bursts")
            return []

    def calculate_relevance_scores(self, events: list[dict], investigation_context: dict) -> list[dict]:
        """Calculate relevance scores for events based on investigation context.

        Args:
            events: List of events to score
            investigation_context: Context from get_investigation_context

        Returns:
            Events with relevance_score field updated
        """
        try:
            target_time = investigation_context.get("target_time")
            if not target_time:
                logger.warning("POI_RELEVANCE_WARNING: No target_time in investigation context")
                return events

            logger.debug("POI_RELEVANCE_START: Scoring %d events against target %s", len(events), target_time)

            scored_events = []

            for event in events:
                try:
                    # Calculate temporal proximity score (0-1, higher = closer to target)
                    event_time = event.get("timestamp")
                    if not event_time:
                        scored_events.append({**event, "relevance_score": 0.0})
                        continue

                    # Time distance in seconds
                    time_delta = abs((event_time - target_time).total_seconds())
                    # Normalize to 0-1 (closer = higher score, max distance = 1 hour)
                    max_distance_seconds = 3600  # 1 hour
                    temporal_score = max(0, 1 - (time_delta / max_distance_seconds))

                    # Severity weighting
                    log_level = event.get("log_level", "INFO")
                    severity_weights = {"ERROR": 1.0, "WARNING": 0.7, "INFO": 0.3, "DEBUG": 0.1}
                    severity_score = severity_weights.get(log_level, 0.3)

                    # Combined relevance score (weighted average)
                    relevance_score = (0.6 * temporal_score) + (0.4 * severity_score)

                    scored_events.append({**event, "relevance_score": relevance_score})

                except Exception as e:
                    logger.warning("POI_RELEVANCE_EVENT_ERROR: Failed to score event: %s", e)
                    scored_events.append({**event, "relevance_score": 0.0})

            # Sort by relevance score (highest first)
            scored_events.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

            logger.debug("POI_RELEVANCE_END: Scored %d events, top score: %.3f", 
                        len(scored_events), scored_events[0].get("relevance_score", 0) if scored_events else 0)

            return scored_events

        except Exception as e:
            logger.exception("POI_RELEVANCE_ERROR: Failed to calculate relevance scores")
            return events