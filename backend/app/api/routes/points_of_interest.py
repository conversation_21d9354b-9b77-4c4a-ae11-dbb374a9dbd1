"""Points of Interest API routes for investigation-focused log analysis."""

from __future__ import annotations

from datetime import datetime

from app.api.dependencies import get_cross_analyzer, get_minknow_analyzer, get_resource_analyzer, get_timeline_analyzer
from app.utils.logging import logger
from app.utils.serialization import prepare_data_for_response
from fastapi import APIRouter, Depends, HTTPException, Query

from log_parser.analysis.cross_analyzer import CrossAnalyzer
from log_parser.analysis.points_of_interest_analyzer import PointsOfInterestAnalyzer
from log_parser.analysis.resource_analyzer import ResourceAnalyzer
from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
from log_parser.analysis.timeline_analyzer import TimelineAnalyzer

# Create router
router = APIRouter(prefix="/poi", tags=["points_of_interest"])


async def get_poi_analyzer(
    structured_analyzer: StructuredLogAnalyzer = Depends(get_minknow_analyzer),
    resource_analyzer: ResourceAnalyzer = Depends(get_resource_analyzer),
    timeline_analyzer: TimelineAnalyzer = Depends(get_timeline_analyzer),
    cross_analyzer: CrossAnalyzer = Depends(get_cross_analyzer),
) -> PointsOfInterestAnalyzer:
    """Create and return POI analyzer instance."""
    return PointsOfInterestAnalyzer(
        structured_analyzer=structured_analyzer,
        resource_analyzer=resource_analyzer,
        timeline_analyzer=timeline_analyzer,
        cross_analyzer=cross_analyzer,
    )


@router.get(
    "/investigation_context",
    summary="Get comprehensive investigation context around a specific time",
    response_description="Investigation context with critical events, timeline data, and resource metrics",
)
async def get_investigation_context(
    target_time: datetime = Query(..., description="Target time for investigation (ISO format)"),
    window_minutes: int = Query(60, description="Time window around target (total window, will be split ±)"),
    position: str | None = Query(None, description="Optional position filter for spatial context"),
    poi_analyzer: PointsOfInterestAnalyzer = Depends(get_poi_analyzer),
):
    """Get comprehensive investigation context around a specific time.
    
    This is the primary endpoint for investigation workflows. It orchestrates
    calls to existing analyzers to provide all relevant context around a
    target time for incident investigation.
    """
    logger.info("Getting investigation context for %s (±%d min, position=%s)", target_time, window_minutes, position)

    try:
        context = poi_analyzer.get_investigation_context(
            target_time=target_time,
            window_minutes=window_minutes,
            position=position,
        )

        # Convert timestamps to ISO format for JSON serialization
        if context.get("target_time"):
            context["target_time"] = context["target_time"].isoformat()
        
        if context.get("window"):
            if context["window"].get("start"):
                context["window"]["start"] = context["window"]["start"].isoformat()
            if context["window"].get("end"):
                context["window"]["end"] = context["window"]["end"].isoformat()

        # Convert timestamps in critical events
        for event in context.get("critical_events", []):
            if event.get("timestamp"):
                event["timestamp"] = event["timestamp"].isoformat()

        # Convert timestamps in timeline events
        for timeline_data in context.get("active_timelines", []):
            timeline_event = timeline_data.get("timeline_event", {})
            if timeline_event.get("start_time"):
                timeline_event["start_time"] = timeline_event["start_time"].isoformat()
            if timeline_event.get("end_time"):
                timeline_event["end_time"] = timeline_event["end_time"].isoformat()
            
            # Convert marker timestamps
            for marker in timeline_data.get("relevant_markers", []):
                if marker.get("timestamp"):
                    marker["timestamp"] = marker["timestamp"].isoformat()

        # Convert timestamps in resource snapshot
        if context.get("resource_snapshot", {}).get("timestamp"):
            context["resource_snapshot"]["timestamp"] = context["resource_snapshot"]["timestamp"].isoformat()

        # Convert timestamps in concurrent activity
        for activity in context.get("concurrent_activity", []):
            for event in activity.get("events", []):
                if event.get("timestamp"):
                    event["timestamp"] = event["timestamp"].isoformat()

        # Convert timestamps in anomalies
        for anomaly in context.get("recent_anomalies", []):
            if anomaly.get("start_time"):
                anomaly["start_time"] = anomaly["start_time"].isoformat()

        # Prepare data for JSON response
        return prepare_data_for_response(context)

    except Exception as e:
        logger.exception("Error getting investigation context")
        raise HTTPException(status_code=500, detail=f"Error getting investigation context: {str(e)}")


@router.get(
    "/event_bursts",
    summary="Detect rapid increases in event counts",
    response_description="List of event burst periods with metadata",
)
async def detect_event_bursts(
    window_minutes: int = Query(5, description="Size of sliding window for burst detection"),
    threshold_count: int = Query(3, description="Minimum events in window to trigger burst"),
    start_time: datetime | None = Query(None, description="Start of analysis period (ISO format)"),
    end_time: datetime | None = Query(None, description="End of analysis period (ISO format)"),
    position: str | None = Query(None, description="Optional position filter"),
    poi_analyzer: PointsOfInterestAnalyzer = Depends(get_poi_analyzer),
):
    """Detect rapid increases in event counts (event bursts) using sliding window analysis."""
    logger.info("Detecting event bursts: window=%d min, threshold=%d, period=%s to %s", 
                window_minutes, threshold_count, start_time, end_time)

    try:
        bursts = poi_analyzer.detect_event_bursts(
            window_minutes=window_minutes,
            threshold_count=threshold_count,
            start_time=start_time,
            end_time=end_time,
            position=position,
        )

        # Convert timestamps to ISO format for JSON serialization
        for burst in bursts:
            if burst.get("start_time"):
                burst["start_time"] = burst["start_time"].isoformat()
            if burst.get("end_time"):
                burst["end_time"] = burst["end_time"].isoformat()

        # Prepare data for JSON response
        return prepare_data_for_response(bursts)

    except Exception as e:
        logger.exception("Error detecting event bursts")
        raise HTTPException(status_code=500, detail=f"Error detecting event bursts: {str(e)}")


@router.post(
    "/relevance_scores",
    summary="Calculate relevance scores for events based on investigation context",
    response_description="Events with relevance scores updated",
)
async def calculate_relevance_scores(
    events: list[dict],
    investigation_context: dict,
    poi_analyzer: PointsOfInterestAnalyzer = Depends(get_poi_analyzer),
):
    """Calculate relevance scores for events based on investigation context.
    
    This endpoint takes a list of events and an investigation context
    and returns the events with relevance scores calculated.
    """
    logger.info("Calculating relevance scores for %d events", len(events))

    try:
        # Convert timestamp strings back to datetime objects if needed
        for event in events:
            if event.get("timestamp") and isinstance(event["timestamp"], str):
                event["timestamp"] = datetime.fromisoformat(event["timestamp"])

        if investigation_context.get("target_time") and isinstance(investigation_context["target_time"], str):
            investigation_context["target_time"] = datetime.fromisoformat(investigation_context["target_time"])

        scored_events = poi_analyzer.calculate_relevance_scores(events, investigation_context)

        # Convert timestamps back to ISO format for JSON serialization
        for event in scored_events:
            if event.get("timestamp"):
                event["timestamp"] = event["timestamp"].isoformat()

        # Prepare data for JSON response
        return prepare_data_for_response(scored_events)

    except Exception as e:
        logger.exception("Error calculating relevance scores")
        raise HTTPException(status_code=500, detail=f"Error calculating relevance scores: {str(e)}")