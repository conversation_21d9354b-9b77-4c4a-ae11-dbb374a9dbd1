from __future__ import annotations

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any

from fastapi import Query

from log_parser.analysis.analyzer_factory import AnalyzerFactory
from log_parser.analysis.cross_analyzer import CrossAnalyzer
from log_parser.analysis.points_of_interest_analyzer import PointsOfInterestAnalyzer
from log_parser.analysis.resource_analyzer import ResourceAnalyzer
from log_parser.analysis.structured_log_analyzer import StructuredLogAnalyzer
from log_parser.analysis.timeline_analyzer import TimelineAnalyzer
from log_parser.models import LogEntry, QuarantineEntry

# Import from the log_parser package
from log_parser.parsers.parser_factory import ParserFactory
from log_parser.utils.time_utils import to_python_datetime

from ..config import settings
from ..utils.logging import logger
from .errors import AnalyzerNotFoundError, AnalyzerProcessingError, InvalidTimestampError


def save_intermediate_data(data: Any, filename: str) -> None:
    """Save intermediate data to disk if configured.

    Args:
        data: The data to save
        filename: Name of the file to save to
    """
    if not settings.save_intermediate_data:
        return

    # Create intermediate data directory if it doesn't exist
    data_dir = Path(settings.intermediate_data_dir)
    data_dir.mkdir(parents=True, exist_ok=True)

    # Save the data
    filepath = data_dir / filename
    try:
        with open(filepath, "w") as f:
            json.dump(data, f, indent=2, default=str)
        logger.info("Saved intermediate data to %s", filepath)
    except Exception:
        logger.exception("Error saving intermediate data to %s", filepath)


# Global storage for parsed log entries
parsed_logs: dict[str, list[LogEntry]] = {"resource": [], "minknow": []}

# Global storage for quarantine entries
quarantine_logs: dict[str, list[QuarantineEntry]] = {"resource": [], "minknow": []}

# Global storage for analyzers
analyzers: dict[str, Any] = {}


async def parse_logs(base_dir: str | None = None) -> dict[str, list[LogEntry]]:
    """Parse log files from the base directory.

    Args:
        base_dir: Base directory containing log files (defaults to settings.base_log_folder)

    Returns:
        Dictionary mapping log types to lists of log entries
    """
    global parsed_logs, quarantine_logs

    if not base_dir:
        base_dir = settings.base_log_folder

    if not base_dir or not os.path.isdir(base_dir):
        logger.warning("Invalid log directory: %s", base_dir)
        return parsed_logs

    logger.info("Parsing logs from %s", base_dir)

    # Create parsers
    resource_parser = ParserFactory.create_parser("resource")
    structured_log_parser = ParserFactory.create_parser("structured")

    # Parse logs
    try:
        resource_dir = os.path.join(base_dir, "ont-resource-logger")
        minknow_dir = os.path.join(base_dir, "minknow")

        # Initialize with empty lists to ensure fields exist even if no logs are found
        parsed_logs["resource"] = []
        parsed_logs["minknow"] = []
        quarantine_logs["resource"] = []
        quarantine_logs["minknow"] = []

        if os.path.isdir(resource_dir):
            resource_logs, resource_quarantine = resource_parser.parse_directory(resource_dir)
            if resource_logs:
                parsed_logs["resource"] = resource_logs
                logger.info("Parsed %d resource log entries", len(parsed_logs["resource"]))
                save_intermediate_data([log.model_dump() for log in resource_logs], "resource_logs.json")
            if resource_quarantine:
                quarantine_logs["resource"] = resource_quarantine
                logger.info("Quarantined %d resource log entries", len(quarantine_logs["resource"]))
                save_intermediate_data([log.model_dump() for log in resource_quarantine], "resource_quarantine.json")
            if not resource_logs and not resource_quarantine:
                logger.warning("No resource log entries found in directory")
        else:
            logger.warning("Resource directory not found: %s", resource_dir)

        if os.path.isdir(minknow_dir):
            minknow_logs, minknow_quarantine = structured_log_parser.parse_directory(minknow_dir)
            if minknow_logs:
                parsed_logs["minknow"] = minknow_logs
                logger.info("Parsed %d minknow log entries", len(parsed_logs["minknow"]))
                save_intermediate_data([log.model_dump() for log in minknow_logs], "minknow_logs.json")
            if minknow_quarantine:
                quarantine_logs["minknow"] = minknow_quarantine
                logger.info("Quarantined %d minknow log entries", len(quarantine_logs["minknow"]))
                save_intermediate_data([log.model_dump() for log in minknow_quarantine], "minknow_quarantine.json")
            if not minknow_logs and not minknow_quarantine:
                logger.warning("No minknow log entries found in directory")
        else:
            logger.warning("Minknow directory not found: %s", minknow_dir)

        # Create analyzers
        analyzers["resource"] = ResourceAnalyzer(parsed_logs["resource"])
        analyzers["minknow"] = StructuredLogAnalyzer(parsed_logs["minknow"])
        analyzers["cross"] = CrossAnalyzer(resource_analyzer=analyzers["resource"], minknow_analyzer=analyzers["minknow"])

        # Create timeline analyzer
        try:
            analyzers["timeline"] = AnalyzerFactory.create_analyzer("timeline", parsed_logs["minknow"])
            logger.info("Created timeline analyzer")

            # Initialize with any timeline events in the logs
            timeline_event_count = analyzers["timeline"].identify_timeline_events()
            logger.info("Identified %d timeline events in logs", timeline_event_count)

            # Save timeline analyzer state if configured
            if settings.save_intermediate_data:
                save_intermediate_data(
                    {
                        "timeline_event_count": timeline_event_count,
                        "available_timeline_events": analyzers["timeline"].get_available_timeline_events(),
                        "timeline_event_hierarchy": analyzers["timeline"].get_timeline_event_hierarchy(),
                        "timeline_event_tree": analyzers["timeline"].get_timeline_event_tree(),
                    },
                    "timeline_analyzer_state.json",
                )
        except Exception:
            logger.exception("Error creating timeline analyzer")
            logger.info("Continuing without timeline analyzer")

        # Create POI analyzer if all required analyzers are available
        try:
            if all(key in analyzers for key in ["minknow", "resource", "timeline", "cross"]):
                analyzers["poi"] = PointsOfInterestAnalyzer(
                    structured_analyzer=analyzers["minknow"],
                    resource_analyzer=analyzers["resource"],
                    timeline_analyzer=analyzers["timeline"],
                    cross_analyzer=analyzers["cross"],
                )
                logger.info("Created POI analyzer")
            else:
                logger.warning("Cannot create POI analyzer - missing required analyzers")
        except Exception:
            logger.exception("Error creating POI analyzer")
            logger.info("Continuing without POI analyzer")

        # Initialize cross analyzer with pre-calculated data
        logger.info("Pre-calculating common analysis data for faster responses...")
        try:
            analyzers["cross"].initialize()
            logger.info("Pre-calculation complete")

            # Save cross analyzer state if configured
            if settings.save_intermediate_data:
                # Get common time range and positions
                common_start, common_end = analyzers["cross"].get_common_time_range()
                positions = analyzers["cross"].get_positions()

                # Get log summary for the common time range
                log_summary = analyzers["cross"].get_log_summary(start=common_start, end=common_end)

                # Get column availability for the common time range
                column_availability = analyzers["cross"].get_column_availability(start=common_start, end=common_end)

                save_intermediate_data(
                    {
                        "common_time_range": {
                            "start": common_start.isoformat() if common_start else None,
                            "end": common_end.isoformat() if common_end else None,
                        },
                        "positions": positions,
                        "log_summary": log_summary,
                        "column_availability": column_availability,
                    },
                    "cross_analyzer_state.json",
                )
        except Exception:
            logger.exception("Error during cross analyzer initialization")
            logger.info("Continuing with basic analyzer initialization")

    except Exception as e:
        logger.exception("Error parsing logs")
        raise AnalyzerProcessingError(f"Error parsing logs: {e}")
    else:
        return parsed_logs


async def get_resource_analyzer() -> ResourceAnalyzer:
    """Get the resource analyzer instance.

    Raises:
        AnalyzerNotFoundError: If analyzer is not available

    Returns:
        The resource analyzer instance
    """
    if "resource" not in analyzers or not isinstance(analyzers["resource"], ResourceAnalyzer):
        if not parsed_logs["resource"]:
            # Try parsing logs if not already parsed
            await parse_logs()

        if "resource" not in analyzers or not isinstance(analyzers["resource"], ResourceAnalyzer):
            logger.warning("Resource analyzer requested but not available")
            raise AnalyzerNotFoundError("No resource analyzer available")

    return analyzers["resource"]


async def get_minknow_analyzer() -> StructuredLogAnalyzer:
    """Get the minknow analyzer instance.

    Raises:
        AnalyzerNotFoundError: If analyzer is not available

    Returns:
        The minknow analyzer instance
    """
    if "minknow" not in analyzers or not isinstance(analyzers["minknow"], StructuredLogAnalyzer):
        if not parsed_logs["minknow"]:
            # Try parsing logs if not already parsed
            await parse_logs()

        if "minknow" not in analyzers or not isinstance(analyzers["minknow"], StructuredLogAnalyzer):
            logger.warning("Minknow analyzer requested but not available")
            raise AnalyzerNotFoundError("No minknow analyzer available")

    return analyzers["minknow"]


async def get_cross_analyzer() -> CrossAnalyzer:
    """Get the cross analyzer instance.

    Raises:
        AnalyzerNotFoundError: If analyzer is not available

    Returns:
        The cross analyzer instance
    """
    if "cross" not in analyzers or not isinstance(analyzers["cross"], CrossAnalyzer):
        # Make sure we have both resource and minknow analyzers
        resource_analyzer = await get_resource_analyzer()
        minknow_analyzer = await get_minknow_analyzer()

        # Create cross analyzer
        analyzers["cross"] = CrossAnalyzer(resource_analyzer=resource_analyzer, minknow_analyzer=minknow_analyzer)

    return analyzers["cross"]


async def get_timeline_analyzer() -> TimelineAnalyzer:
    """Get the timeline analyzer instance.

    Raises:
        AnalyzerNotFoundError: If analyzer is not available

    Returns:
        The timeline analyzer instance
    """
    if "timeline" not in analyzers or not isinstance(analyzers["timeline"], TimelineAnalyzer):
        if not parsed_logs["minknow"]:
            # Try parsing logs if not already parsed
            await parse_logs()

        # Create timeline analyzer using factory
        try:
            analyzers["timeline"] = AnalyzerFactory.create_analyzer("timeline", parsed_logs["minknow"])
            logger.info("Timeline analyzer created")
        except Exception:
            logger.exception("Error creating timeline analyzer")
            raise AnalyzerNotFoundError("Failed to create timeline analyzer")

    return analyzers["timeline"]


async def get_poi_analyzer() -> PointsOfInterestAnalyzer:
    """Get the POI analyzer instance.

    Raises:
        AnalyzerNotFoundError: If analyzer is not available

    Returns:
        The POI analyzer instance
    """
    if "poi" not in analyzers or not isinstance(analyzers["poi"], PointsOfInterestAnalyzer):
        # Ensure all required analyzers are available
        structured_analyzer = await get_minknow_analyzer()
        resource_analyzer = await get_resource_analyzer()
        timeline_analyzer = await get_timeline_analyzer()
        cross_analyzer = await get_cross_analyzer()

        # Create POI analyzer
        analyzers["poi"] = PointsOfInterestAnalyzer(
            structured_analyzer=structured_analyzer,
            resource_analyzer=resource_analyzer,
            timeline_analyzer=timeline_analyzer,
            cross_analyzer=cross_analyzer,
        )
        logger.info("POI analyzer created on demand")

    return analyzers["poi"]


async def normalize_timestamp(timestamp: datetime | None = None) -> datetime | None:
    """Normalize timestamp by converting timezone-aware to timezone-naive.

    Args:
        timestamp: The timestamp to normalize

    Returns:
        Normalized timestamp
    """
    # First ensure it's a Python datetime
    timestamp = to_python_datetime(timestamp)

    # Then remove timezone if present
    if timestamp and timestamp.tzinfo:
        return timestamp.replace(tzinfo=None)
    return timestamp


async def validate_timerange(
    start: datetime = Query(..., description="Start timestamp (ISO format)"),
    end: datetime = Query(..., description="End timestamp (ISO format)"),
) -> tuple[datetime, datetime]:
    """Validate and normalize a time range.

    Args:
        start: Start timestamp
        end: End timestamp

    Raises:
        InvalidTimestampError: If end is before start

    Returns:
        Tuple of normalized start and end timestamps
    """
    # Normalize timestamps
    start = await normalize_timestamp(start)
    end = await normalize_timestamp(end)

    # Validate time range
    if end < start:
        raise InvalidTimestampError("End timestamp must be after start timestamp")

    return start, end


async def parse_columns_param(
    columns: str | None = Query(None, description="Comma-separated list of columns to include"),
) -> list[str] | None:
    """Parse comma-separated column list parameter.

    Args:
        columns: Comma-separated list of columns

    Returns:
        List of column names or None
    """
    if not columns:
        return None

    return [col.strip() for col in columns.split(",") if col.strip()]


async def parse_positions_param(
    positions: str | None = Query(None, description="Comma-separated list of positions to filter by"),
) -> list[str] | None:
    """Parse comma-separated positions list parameter.

    Args:
        positions: Comma-separated list of positions

    Returns:
        List of position names or None
    """
    if not positions:
        return None

    return [pos.strip() for pos in positions.split(",") if pos.strip()]
