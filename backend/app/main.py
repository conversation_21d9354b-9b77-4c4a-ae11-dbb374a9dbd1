from __future__ import annotations

import json
import logging
import signal
import threading
import time

import numpy as np
from fastapi import FastAP<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from log_parser.analysis.analyzer_factory import AnalyzerFactory

from .api.dependencies import analyzers, parse_logs
from .api.errors import (
    AnalyzerNotFoundError,
    AnalyzerProcessingError,
    InvalidTimestampError,
    analyzer_not_found_handler,
    analyzer_processing_error_handler,
    generic_exception_handler,
    invalid_timestamp_error_handler,
    validation_exception_handler,
)
from .api.routes import (
    analysis_router,
    general_router,
    minknow_router,
    poi_router,
    resource_router,
    structured_router,
    timeline_router,
)

# Use relative imports for all modules
from .config import settings
from .utils.logging import logger, setup_logging
from .utils.serialization import convert_to_serializable

# Configure logging based on settings
logger = setup_logging(settings.log_level)

# Flag to track if the application is shutting down
is_shutting_down = False

# Thread lock for synchronizing signal handlers
shutdown_lock = threading.Lock()


# Signal handler for graceful shutdown
def handle_shutdown_signal(signum, frame):
    """Handle shutdown signals gracefully by terminating background threads."""
    global is_shutting_down

    # Use a lock to prevent concurrent shutdown operations
    if not shutdown_lock.acquire(blocking=False):
        # Another thread is already handling shutdown
        print("Shutdown already in progress in another thread")
        import os

        os._exit(1)

    # Use print instead of logger to avoid reentrant calls during shutdown
    if is_shutting_down:
        print("Received second shutdown signal, forcing exit")
        # Use os._exit to exit immediately without further cleanup
        import os

        os._exit(1)

    is_shutting_down = True
    signal_name = "SIGTERM" if signum == signal.SIGTERM else "SIGINT"
    print(f"Received {signal_name} signal, initiating graceful shutdown...")

    # Disable logging to avoid reentrant calls
    logging.disable(logging.CRITICAL)

    # Gracefully shut down background threads
    try:
        if "cross" in analyzers and hasattr(analyzers["cross"], "shutdown"):
            print("Shutting down CrossAnalyzer background threads...")
            analyzers["cross"].shutdown()
    except Exception as e:
        print(f"Error shutting down background threads: {e}")

    # Exit with a successful status code
    print("Shutdown complete, exiting...")

    # Release the lock before exiting
    shutdown_lock.release()

    # Use os._exit to exit immediately without triggering more cleanup code
    import os

    os._exit(0)


# Register signal handlers
signal.signal(signal.SIGINT, handle_shutdown_signal)
signal.signal(signal.SIGTERM, handle_shutdown_signal)


# Custom JSON encoder for NumPy types
class NumpyJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.bool_):
            return bool(obj)
        return super().default(obj)


# Custom response class that can handle the background parameter
class CustomJSONResponse(JSONResponse):
    def __init__(
        self,
        content=None,
        status_code=200,
        headers=None,
        media_type=None,
        background=None,
        **kwargs,
    ):
        # Ignore background parameter
        super().__init__(
            content=content,
            status_code=status_code,
            headers=headers,
            media_type=media_type,
            **kwargs,
        )

    def render(self, content):
        # Ensure content is serializable
        serialized_content = convert_to_serializable(content)
        return super().render(serialized_content)


# Create FastAPI app
app = FastAPI(
    title="LogParser API",
    description="""
    REST API for parsing and querying log files.

    This API provides endpoints to:
    * Parse log files from a specified directory
    * Query resource system logs with time-range filtering
    * Query Minknow logs with various filters
    * Get aggregated log data for visualization
    """,
    version="1.0.0",
    # Use our custom JSON response class
    default_response_class=CustomJSONResponse,
)

# Add CORS middleware
origins = ["*"]  # Adjust allowed origins as needed
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add exception handlers
app.add_exception_handler(AnalyzerNotFoundError, analyzer_not_found_handler)
app.add_exception_handler(AnalyzerProcessingError, analyzer_processing_error_handler)
app.add_exception_handler(InvalidTimestampError, invalid_timestamp_error_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, generic_exception_handler)

# Include routers
app.include_router(general_router)
app.include_router(resource_router)
app.include_router(minknow_router)
app.include_router(analysis_router)
app.include_router(timeline_router)
app.include_router(structured_router)
app.include_router(poi_router)


# Startup event to load base log folder and parse logs during init
@app.on_event("startup")
async def startup_event():
    if settings.base_log_folder:
        try:
            logger.info("Starting application with logs from %s", settings.base_log_folder)
            logger.info("This initial load will parse logs and pre-calculate common queries for faster responses")

            # Parse logs and initialize analyzers
            start_time = time.time()
            await parse_logs(settings.base_log_folder)
            end_time = time.time()

            logger.info("Startup initialization completed in %.2f seconds", end_time - start_time)
            logger.info("The application is now ready to handle requests with optimized response times")
        except Exception:
            logger.exception("Error during startup log parsing")
    else:
        logger.warning("BASE_LOG_FOLDER environment variable not set; skipping initial log parsing")


# Middleware for request logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Middleware to log all incoming requests."""
    logger.info("Request: %s %s", request.method, request.url.path)
    response = await call_next(request)
    logger.info("Response: %s %s - Status: %d", request.method, request.url.path, response.status_code)
    return response


# Shutdown event to clean up resources
@app.on_event("shutdown")
async def shutdown_event():
    """Handle application shutdown. Clean up resources and terminate background threads."""
    # Skip if we're already in the process of shutting down from a signal handler
    if is_shutting_down:
        logger.info("Application already shutting down via signal handler")
        return

    logger.info("Application shutdown event triggered, cleaning up resources...")

    # Gracefully shut down background threads if not already done by signal handler
    try:
        if "cross" in analyzers and hasattr(analyzers["cross"], "shutdown"):
            logger.info("Shutting down CrossAnalyzer background threads...")
            analyzers["cross"].shutdown()
    except Exception:
        logger.exception("Error shutting down background threads")

    logger.info("Shutdown cleanup complete")


def create_app(action_config_path: str | None = None) -> FastAPI:
    """Create and configure the FastAPI application.

    Args:
        action_config_path: Optional path to custom action configuration file
    """
    global app

    # Set custom action config if provided
    if action_config_path:
        logger.info("Using custom action configuration: %s", action_config_path)
        try:
            AnalyzerFactory.set_custom_action_config(action_config_path)
            logger.info("Custom action configuration loaded successfully")
        except Exception:
            logger.exception("Error loading custom action config")
            logger.warning("Falling back to default action configuration")
    else:
        logger.info("Using default action configuration")

    return app


# Create default app instance
app = create_app()
