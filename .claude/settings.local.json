{"permissions": {"allow": ["<PERSON><PERSON>(source:*)", "Bash(hatch run dev:*)", "<PERSON><PERSON>(python:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(git add:*)", "Bash(ls:*)", "Bash(hatch run test-tern:*)", "Bash(grep:*)", "WebFetch(domain:dash.plotly.com)", "WebFetch(domain:community.plotly.com)", "Bash(.venv/bin/python:*)", "Bash(git worktree add:*)", "Bash(git worktree remove:*)", "Bash(git branch:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(pre-commit run bandit:*)", "<PERSON><PERSON>(pre-commit run:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:www.dash-mantine-components.com)", "<PERSON><PERSON>(hatch run test:*)"], "deny": []}}